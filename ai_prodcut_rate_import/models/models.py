import logging
from odoo import models, fields, api
from odoo.exceptions import UserError

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class StockMoveLine(models.Model):
    _inherit = 'stock.move.line'

    standard_price = fields.Float(string='Lot Price')

class StockPicking(models.Model):
    _inherit = 'stock.picking'

    def import_standard_price(self):
        logger.info("Starting standard price import for pickings: %s", self.ids)
        for picking in self:
            logger.debug("Processing picking ID: %s", picking.id)
            for line in picking.move_line_ids:
                logger.debug("Processing move line ID: %s", line.id)
                if not line.lot_id:
                    logger.error("Missing lot number for move line ID: %s", line.id)
                    raise UserError("Please specify a lot number for all move lines.")
                if line.standard_price <= 0:
                    logger.error("Lot price <= 0 for lot %s (move line ID: %s)", line.lot_id.name, line.id)
                    raise UserError("Lot price must be greater than zero.")

                # Validate lot number and ensure it belongs to the correct product
                lot = self.env['stock.lot'].search([
                    ('name', '=', line.lot_id.name),
                    ('product_id', '=', line.product_id.id)
                ], limit=1)
                if not lot:
                    logger.error("Lot number %s for product %s does not exist (move line ID: %s)", line.lot_id.name, line.product_id.display_name, line.id)
                    raise UserError(f"Lot number {line.lot_id.name} for product {line.product_id.display_name} does not exist.")

                if lot.product_id.id != line.product_id.id:
                    logger.warning("Lot %s belongs to product %s, but move line is for product %s (move line ID: %s)", lot.name, lot.product_id.display_name, line.product_id.display_name, line.id)
                    raise UserError(f"Lot {lot.name} does not belong to the selected product {line.product_id.display_name}.")

                logger.info("Updating avg_cost_per_weight for lot %s (product %s) to %s", lot.name, lot.product_id.display_name, line.standard_price)
                lot.avg_cost_per_weight = line.standard_price

                lot.standard_price = lot.avg_cost_per_weight * line.product_id.weight  # Update the lot's standard price directly
                logger.info("Updated standard_price for lot %s (product %s) to %s", lot.name, lot.product_id.display_name, lot.standard_price)
                # line.message_post(body=f"Lot price for lot {line.lot_id.name} updated to {line.standard_price}.")
