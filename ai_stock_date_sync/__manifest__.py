{
    'name': 'Stock Date Synchronization',
    'version': '1.0',
    'category': 'Inventory',
    'summary': 'Synchronize effective dates with scheduled dates in stock pickings',
    'description': """
Stock Date Synchronization
=========================
This module provides tools to synchronize the effective date (date_done) of stock pickings with their scheduled date.

Features
--------
* Scheduled action to automatically update effective dates
* Batch action button in list view to update selected pickings
* Maintains data consistency across all related records

Data Consistency
---------------
When updating the effective date (date_done) of stock pickings, the module ensures:

* Stock moves and move lines are updated with the new date
* Stock valuation layers are updated with the correct date
* Accounting entries linked to the picking are updated
* Stock quants are updated with correct in/out dates
* Landed costs are updated if they exist
* Lot/serial numbers are updated for incoming pickings
* Detailed audit logs are created for all changes

This ensures that inventory valuation, accounting, and reporting remain accurate after date changes.

Technical Implementation
----------------------
* Uses the same data consistency mechanisms as the ai_admin_date_edit module
* Provides both automated (scheduled action) and manual (batch action) methods
* Includes detailed logging for audit purposes
    """,
    'author': 'Arihant AI',
    'website': 'https://www.arihantai.com',
    'depends': [
        'stock',
        'ai_admin_date_edit',  # Dependency to reuse data consistency mechanisms
    ],
    'data': [
        'security/security.xml',
        'views/stock_picking_views.xml',
        'data/ir_cron_data.xml',
    ],
    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
}
