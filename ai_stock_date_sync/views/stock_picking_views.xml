<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Add server action for batch processing -->
    <record id="action_sync_effective_date" model="ir.actions.server">
        <field name="name">Sync Effective Date with Scheduled Date</field>
        <field name="model_id" ref="stock.model_stock_picking"/>
        <field name="binding_model_id" ref="stock.model_stock_picking"/>
        <field name="binding_view_types">list</field>
        <field name="state">code</field>
        <field name="groups_id" eval="[(4, ref('ai_stock_date_sync.group_stock_date_sync'))]"/>
        <field name="code">
if records:
    action = records.action_sync_effective_date()
        </field>
    </record>
    
    <!-- Add button to stock picking form view -->
    <record id="view_picking_form_inherit_date_sync" model="ir.ui.view">
        <field name="name">stock.picking.form.inherit.date.sync</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
            <xpath expr="//header" position="inside">
                <button name="action_sync_effective_date" 
                        string="Sync Effective Date" 
                        type="object" 
                        groups="ai_stock_date_sync.group_stock_date_sync"
                        disabled="(state != 'done' or not scheduled_date)"
                        class="btn-primary"/>
            </xpath>
        </field>
    </record>
</odoo>
