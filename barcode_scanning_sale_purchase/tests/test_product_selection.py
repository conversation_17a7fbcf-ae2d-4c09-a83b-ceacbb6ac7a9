# -*- coding: utf-8 -*-
from odoo.tests import common, tagged


@tagged('post_install', '-at_install')
class TestProductSelection(common.TransactionCase):
    """Test the product selection functionality when scanning barcodes."""

    def setUp(self):
        super(TestProductSelection, self).setUp()
        # Create test products
        self.product1 = self.env['product.product'].create({
            'name': 'Test Product 1',
            'type': 'product',
            'default_code': 'TP1',
        })
        self.product2 = self.env['product.product'].create({
            'name': 'Test Product 2',
            'type': 'product',
            'default_code': 'TP2',
        })
        
        # Create a test lot number that's associated with both products
        self.lot_name = 'TEST123'
        self.lot1 = self.env['stock.lot'].create({
            'name': self.lot_name,
            'product_id': self.product1.id,
            'company_id': self.env.company.id,
        })
        self.lot2 = self.env['stock.lot'].create({
            'name': self.lot_name,
            'product_id': self.product2.id,
            'company_id': self.env.company.id,
        })
        
        # Create a stock move
        self.stock_move = self.env['stock.move'].create({
            'name': 'Test Move',
            'product_id': self.product1.id,
            'product_uom': self.product1.uom_id.id,
            'product_uom_qty': 1.0,
            'location_id': self.env.ref('stock.stock_location_stock').id,
            'location_dest_id': self.env.ref('stock.stock_location_customers').id,
        })

    def test_product_selection_wizard_no_default(self):
        """Test that the product selection wizard doesn't select a default product."""
        # Set the barcode_scan field to trigger the onchange
        self.stock_move.barcode_scan = self.lot_name
        
        # Get the products by lot
        filtered_product_ids = self.stock_move.get_products_by_lot()
        self.assertEqual(len(filtered_product_ids), 2, "Should find 2 products for the lot")
        
        # Create the wizard directly
        wizard = self.env['product.lot.selection.wizard'].with_context({
            'default_lot_number': self.lot_name,
            'default_stock_move_id': self.stock_move.id,
            'default_available_product_ids': [(6, 0, filtered_product_ids)],
        }).create({})
        
        # Check that no product is selected by default
        self.assertFalse(wizard.selected_product_id, "No product should be selected by default")
        
        # Select a product and confirm
        wizard.selected_product_id = self.product2.id
        result = wizard.action_select_product()
        
        # Check that the product was set on the stock move
        self.assertEqual(self.stock_move.product_id, self.product2, 
                         "The selected product should be set on the stock move")
