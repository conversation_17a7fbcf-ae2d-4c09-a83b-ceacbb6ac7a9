# Domain Filter Implementation for Barcode Scanning Module

## Overview

This document describes the implementation of a proper domain filter system for the `barcode_scanning_sale_purchase` module. The new implementation replaces the previous approach with a more robust and Odoo 18-compatible domain filtering mechanism.

## Key Changes

### 1. Domain Filter via Onchange

**Before:** Used computed fields with domain attributes in views
**After:** Uses onchange methods to return domain filters directly

### 2. Proper Odoo Domain Format

**Before:** Returned product IDs as strings like `"[1, 2, 3]"`

**After:** Returns proper Odoo domain expressions via onchange:
- `{'domain': {'product_id': []}}` - Shows all products (empty domain)
- `{'domain': {'product_id': [('id', 'in', [1, 2, 3])]}}` - Shows only specific products
- `{'domain': {'product_id': [('id', '!=', 5)]}}` - Excludes specific product (for MRP context)

### 3. View Integration

Views no longer need domain attributes - domains are returned by onchange:

```xml
<field name="barcode_scan" string="Lot No" force_save="1"/>
<field name="product_id" options="{'no_create': True}"/>
```

### 4. Debug Field (Optional)

For debugging purposes, the computed field is still available:

```xml
<field name="product_domain_filter" string="Domain Filter (Debug)" optional="hide"/>
```

## Implementation Details

### Sale Order Line (`sale_order_line.py`)

```python
@api.depends('barcode_scan')
def _compute_product_domain_filter(self):
    """Compute the domain filter for product_id field based on barcode_scan"""
    for line in self:
        if not line.barcode_scan:
            # Show all products when no barcode is scanned
            line.product_domain_filter = "[(1, '=', 1)]"
            continue

        # Check if there are products associated with this lot number
        lots = self.env['stock.lot'].search([('name', '=', line.barcode_scan)])
        if lots:
            product_ids = lots.mapped('product_id.id')
            if product_ids:
                # Show only products associated with this lot number
                line.product_domain_filter = str([('id', 'in', product_ids)])
                continue

        # If no lots found or no products associated with lots, show all products
        line.product_domain_filter = "[(1, '=', 1)]"
```

### Stock Move (`stock_move.py`) - Enhanced for MRP Context

The stock move implementation includes special handling for Manufacturing Orders:

```python
@api.depends('barcode_scan', 'raw_material_production_id', 'other_material_production_id')
def _compute_product_domain_filter(self):
    """Compute the domain filter for product_id field based on barcode_scan"""
    for move in self:
        # Get the parent product ID from MRP context
        parent_product_id = False
        if move.raw_material_production_id and move.raw_material_production_id.product_id:
            parent_product_id = move.raw_material_production_id.product_id.id
        elif move.other_material_production_id and move.other_material_production_id.product_id:
            parent_product_id = move.other_material_production_id.product_id.id

        if not move.barcode_scan:
            # Show all products except parent product when no barcode is scanned
            if parent_product_id:
                move.product_domain_filter = str([('id', '!=', parent_product_id)])
            else:
                move.product_domain_filter = "[(1, '=', 1)]"
            continue

        # ... rest of the logic with parent product filtering
```

### Simplified Onchange Methods

The onchange methods have been simplified to focus on product selection rather than domain manipulation:

```python
@api.onchange('barcode_scan')
def _onchange_barcode_scan(self):
    """Onchange function for searching product using their barcode or lot/serial number
    while preserving the entered lot number value"""
    if not self.barcode_scan:
        # Clear product selection when barcode is cleared
        self.product_id = False
        return

    # First try to find product by barcode
    product = self.env['product.product'].search(
        [('barcode', '=', self.barcode_scan)], limit=1)

    if product:
        self.product_id = product
        return

    # If no product found by barcode, try to find by lot/serial number
    lots = self.env['stock.lot'].search([('name', '=', self.barcode_scan)])

    if lots:
        products = lots.mapped('product_id')

        # If products found for the lot, set the first one
        if products:
            if len(products) == 1:
                # Only one product found, set it directly
                self.product_id = products[0]
            else:
                # Multiple products found, set the first one
                # The domain filter will show only relevant products
                self.product_id = products[0]
```

## Benefits

### 1. **Proper Domain Filtering**
- Uses standard Odoo domain expressions
- Compatible with Odoo 18 field domain attributes
- No more onchange domain returns

### 2. **Better User Experience**
- Product dropdown automatically filters based on scanned lot
- Shows all products when no lot is scanned
- Maintains lot number value after product selection

### 3. **MRP Context Awareness**
- Automatically excludes parent product in manufacturing orders
- Prevents circular references in BOM structures
- Maintains existing MRP functionality

### 4. **Consistent Implementation**
- Same approach across all models (Sale, Purchase, Account, Stock)
- Unified domain filter logic
- Easier maintenance and debugging

## Testing

New test cases have been added in `test_domain_filter.py` to verify:

1. Domain filter shows all products when no barcode is scanned
2. Domain filter shows only relevant products when lot is scanned
3. Domain filter shows all products when invalid lot is scanned
4. Domain filter excludes parent product in MRP context
5. Onchange correctly sets products based on barcode scanning

## Migration Notes

### For Developers

1. **Field Name Change**: `product_domain` → `product_domain_filter`
2. **Domain Format**: Now uses proper Odoo domain expressions
3. **View Updates**: All views updated to use new field
4. **No More Onchange Domains**: Domain filtering is now handled by computed fields

### For Users

1. **Improved Filtering**: Product dropdowns now filter automatically
2. **Better Performance**: No more onchange domain calculations
3. **Consistent Behavior**: Same experience across all modules
4. **Lot Persistence**: Lot numbers are preserved after product selection

## Current Implementation Status

### **FIXED: Domain Filter Now Working!**

**Root Cause Identified**: Domain attributes in list views of one-to-many fields don't work in Odoo 18. Even static domains like `[('type', '=', 'product')]` are ignored.

**Solution**: Use **onchange domain returns only** - this is the only reliable method for dynamic domain filtering in list views.

### **How It Works:**

1. **Onchange Method**: Returns `{'domain': {'product_id': [('id', 'in', [66, 15])]}}` for immediate updates
2. **No View Domain Attributes**: Removed all domain attributes from views since they don't work in list views
3. **User Notifications**: Added warnings to inform users when filtering is applied

### **NEW FEATURE: Product Selection Wizard with Stock Information**

When multiple products are found for the same lot number, the system now opens a wizard that shows:

1. **Product List**: All products associated with the lot number
2. **Stock Information**: Location-wise stock details for each product
3. **Selection Interface**: Toggle buttons to select the desired product
4. **Stock Details Tab**: Detailed view of on-hand, reserved, and available quantities by location

### **Testing Instructions:**

1. **Restart Odoo server** to load updated code
2. **Go to Sales Order** and add a new line
3. **Enter "A125E16001" in Lot No field**
4. **If single product**: Product is selected automatically and dropdown is filtered
5. **If multiple products**: Wizard opens showing all products with stock information
6. **Select desired product** in wizard and click "Select Product"
7. **Product is set** in the original line and wizard closes

## Future Enhancements

1. **Wizard Integration**: Could integrate with product selection wizard for multiple products
2. **Barcode Validation**: Could add barcode format validation
3. **Performance Optimization**: Could cache lot-product mappings
4. **Advanced Filtering**: Could add additional filter criteria beyond lot numbers
