<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <!--   View for purchase order -->
    <record id="purchase_order_form" model="ir.ui.view">
        <field name="name">
            purchase.order.view.form.inherit.barcode.scanning.sale.purchase
        </field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='order_line']/list/field[@name='product_id']"
                   position="before">
                <field name="barcode_scan" string="Lot No" force_save="1"/>
                <button name="action_open_product_wizard" string="Select" type="object"
                        icon="fa-search" invisible="not barcode_scan or parent.state not in ('draft', 'sent', 'to approve')"
                        help="Open wizard to select product for this lot number"/>
            </xpath>

            <xpath expr="//field[@name='order_line']/list/field[@name='product_id']"
                   position="attributes">
                <attribute name="options">{'no_create': True}</attribute>
            </xpath>
        </field>
    </record>
</odoo>
