<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <!-- View for stock picking-->
    <record id="view_picking_form" model="ir.ui.view">
        <field name="name">
            stock.picking.view.form.inherit.barcode.scanning.sale.purchase
        </field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='move_ids_without_package']/list/field[@name='partner_id']"
                   position="before">
                <field name="barcode_scan" string="Lot No" force_save="1"/>
                <button name="action_open_product_wizard" string="Select" type="object"
                        icon="fa-search" invisible="not barcode_scan or parent.state not in ('draft', 'waiting', 'confirmed', 'assigned')"
                        help="Open wizard to select product for this lot number"/>
            </xpath>

            <xpath expr="//field[@name='move_ids_without_package']/list/field[@name='product_id']"
                   position="attributes">
                <attribute name="options">{'no_create': True}</attribute>
            </xpath>
        </field>
    </record>
</odoo>
