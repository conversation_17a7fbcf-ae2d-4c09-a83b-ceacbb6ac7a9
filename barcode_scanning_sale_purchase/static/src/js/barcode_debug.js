odoo.define('barcode_scanning_sale_purchase.debug', function (require) {
    'use strict';

    var core = require('web.core');
    var Dialog = require('web.Dialog');
    var _t = core._t;

    // Add a global error handler to catch and display any errors
    window.addEventListener('error', function(event) {
        console.error('JavaScript error:', event.message, 'at', event.filename, ':', event.lineno);
        
        // Show a dialog with the error
        new Dialog(null, {
            title: _t('JavaScript Error'),
            $content: $('<div>').html(
                '<p><strong>Error:</strong> ' + event.message + '</p>' +
                '<p><strong>File:</strong> ' + event.filename + '</p>' +
                '<p><strong>Line:</strong> ' + event.lineno + '</p>'
            ),
            buttons: [{
                text: _t('OK'),
                close: true
            }]
        }).open();
        
        return false;
    });

    // Log when action windows are opened
    var ActionManager = require('web.ActionManager');
    var originalDoAction = ActionManager.prototype.doAction;
    
    ActionManager.prototype.doAction = function(action, options) {
        console.log('Action being executed:', action);
        
        if (action && action.res_model === 'product.lot.selection.wizard') {
            console.log('Product selection wizard being opened with context:', action.context);
        }
        
        return originalDoAction.apply(this, arguments);
    };
});
