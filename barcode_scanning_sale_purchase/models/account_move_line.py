# -*- coding: utf-8 -*-
################################################################################
#
#    Cybrosys Technologies Pvt. Ltd.
#
#    Copyright (C) 2024-TODAY Cybrosys Technologies(<https://www.cybrosys.com>).
#    Author:  <PERSON><PERSON> (<EMAIL>)
#
#    You can modify it under the terms of the GNU AFFERO
#    GENERAL PUBLIC LICENSE (AGPL v3), Version 3.
#
#    This program is distributed in the hope that it will be useful,
#    but WITHOUT ANY WARRANTY; without even the implied warranty of
#    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#    GNU AFFERO GENERAL PUBLIC LICENSE (AGPL v3) for more details.
#
#    You should have received a copy of the GNU AFFERO GENERAL PUBLIC LICENSE
#    (AGPL v3) along with this program.
#    If not, see <http://www.gnu.org/licenses/>.
#
################################################################################
from odoo import api, fields, models


class AccountMoveLine(models.Model):
    """Inherited the model for to add field for barcode."""
    _inherit = 'account.move.line'

    barcode_scan = fields.Char(string='Lot No',
                               help="Enter the lot number or scan product barcode. "
                                    "The system will find the product while preserving the lot number value.")



    @api.onchange('barcode_scan')
    def _onchange_barcode_scan(self):
        """Onchange function for searching product using their barcode or lot/serial number
        while preserving the entered lot number value"""
        if not self.barcode_scan:
            # Clear product selection when barcode is cleared
            self.product_id = False
            # Return empty domain to show all products
            return {'domain': {'product_id': []}}

        # First try to find product by barcode
        product = self.env['product.product'].search(
            [('barcode', '=', self.barcode_scan)], limit=1)

        if product:
            self.product_id = product
            # Return empty domain since we found a specific product
            return {'domain': {'product_id': []}}

        # If no product found by barcode, try to find by lot/serial number
        lots = self.env['stock.lot'].search([('name', '=', self.barcode_scan)])

        if lots:
            products = lots.mapped('product_id')
            product_ids = products.ids

            if products:
                if len(products) == 1:
                    # Only one product found, set it directly
                    self.product_id = products[0]
                    return {'domain': {'product_id': [('id', 'in', product_ids)]}}
                else:
                    # Multiple products found, set first product and filter domain
                    self.product_id = products[0]
                    return {'domain': {'product_id': [('id', 'in', product_ids)]}}

        # No lots or products found, show all products
        return {'domain': {'product_id': []}}



    def action_open_product_wizard(self):
        """Open wizard to select product when multiple products are found for the same lot"""
        self.ensure_one()

        if not self.barcode_scan:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Lot Number',
                    'message': 'Please enter a lot number first.',
                    'type': 'warning',
                }
            }

        # Find products for this lot number
        lots = self.env['stock.lot'].search([('name', '=', self.barcode_scan)])
        if not lots:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Lots Found',
                    'message': f'No lots found for lot number {self.barcode_scan}',
                    'type': 'warning',
                }
            }

        products = lots.mapped('product_id')
        if not products:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Products Found',
                    'message': f'No products found for lot number {self.barcode_scan}',
                    'type': 'warning',
                }
            }

        # Create wizard record
        wizard = self.env['product.selection.wizard'].create({
            'lot_number': self.barcode_scan,
            'source_model': self._name,
            'source_record_id': self.id,
        })

        # Create wizard lines for each product
        wizard_lines = []
        for product in products:
            wizard_lines.append((0, 0, {
                'product_id': product.id,
                'is_selected': False,
            }))

        wizard.line_ids = wizard_lines

        # Return action to open wizard
        return {
            'name': f'Select Product for Lot {self.barcode_scan}',
            'type': 'ir.actions.act_window',
            'res_model': 'product.selection.wizard',
            'res_id': wizard.id,
            'view_mode': 'form',
            'target': 'new',
        }