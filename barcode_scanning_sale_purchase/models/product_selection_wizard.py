# -*- coding: utf-8 -*-
from odoo import api, fields, models
import logging

_logger = logging.getLogger(__name__)


class ProductSelectionWizard(models.TransientModel):
    _name = 'product.selection.wizard'
    _description = 'Product Selection Wizard for Multiple Products with Same Lot'

    lot_number = fields.Char(string='Lot Number', readonly=True)
    source_model = fields.Char(string='Source Model', readonly=True)
    source_record_id = fields.Integer(string='Source Record ID', readonly=True)
    line_ids = fields.One2many('product.selection.wizard.line', 'wizard_id', string='Available Products')

    def action_select_product(self):
        """Apply the selected product to the source record"""
        self.ensure_one()

        # Find the selected line
        selected_line = self.line_ids.filtered('is_selected')

        if not selected_line:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Product Selected',
                    'message': 'Please select a product before continuing.',
                    'type': 'warning',
                }
            }

        if len(selected_line) > 1:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Multiple Products Selected',
                    'message': 'Please select only one product.',
                    'type': 'warning',
                }
            }

        # Get the source record and update it
        try:
            source_model = self.env[self.source_model]
            source_record = source_model.browse(self.source_record_id)

            if source_record.exists():
                # Update the product_id field using write method for better reliability
                source_record.write({'product_id': selected_line.product_id.id})

                _logger.info("Updated %s record %s with product %s",
                           self.source_model, self.source_record_id, selected_line.product_id.name)

                # Close the wizard and refresh the view
                return {
                    'type': 'ir.actions.act_window_close',
                }
            else:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': 'Error',
                        'message': 'Source record not found.',
                        'type': 'danger',
                    }
                }

        except Exception as e:
            _logger.error("Error updating source record: %s", str(e))
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Error',
                    'message': f'Error updating record: {str(e)}',
                    'type': 'danger',
                }
            }


class ProductSelectionWizardLine(models.TransientModel):
    _name = 'product.selection.wizard.line'
    _description = 'Product Selection Wizard Line'

    wizard_id = fields.Many2one('product.selection.wizard', string='Wizard', ondelete='cascade')
    product_id = fields.Many2one('product.product', string='Product', required=True)
    is_selected = fields.Boolean(string='Select', default=False)

    @api.onchange('is_selected')
    def _onchange_is_selected(self):
        """Ensure only one product is selected at a time"""
        if self.is_selected:
            # Unselect all other lines
            for line in self.wizard_id.line_ids:
                if line.id != self.id:
                    line.is_selected = False

    # Product information
    product_code = fields.Char(related='product_id.default_code', string='Product Code', readonly=True)
    product_name = fields.Char(related='product_id.name', string='Product Name', readonly=True)
    product_uom = fields.Char(related='product_id.uom_id.name', string='UoM', readonly=True)

    # Stock information
    total_qty_available = fields.Float(string='Total Available Qty', compute='_compute_stock_info', store=False)
    stock_info_ids = fields.One2many('product.selection.wizard.stock', 'line_id', string='Stock by Location')
    stock_details_text = fields.Text(string='Stock Details', compute='_compute_stock_details_text', store=False)

    @api.depends('product_id')
    def _compute_stock_info(self):
        """Compute total available quantity"""
        for line in self:
            if line.product_id:
                line.total_qty_available = line.product_id.qty_available
            else:
                line.total_qty_available = 0.0

    @api.model
    def create(self, vals):
        """Override create to populate stock information"""
        record = super().create(vals)
        if record.product_id:
            record._populate_stock_info()
        return record

    def _populate_stock_info(self):
        """Populate stock information by location"""
        self.ensure_one()

        if not self.product_id:
            return

        # Get stock quants for this product
        quants = self.env['stock.quant'].search([
            ('product_id', '=', self.product_id.id),
            ('quantity', '>', 0),
            ('location_id.usage', '=', 'internal')  # Only internal locations
        ])

        # Group by location
        location_stock = {}
        for quant in quants:
            location = quant.location_id
            if location.id not in location_stock:
                location_stock[location.id] = {
                    'location': location,
                    'quantity': 0.0,
                    'reserved_quantity': 0.0
                }
            location_stock[location.id]['quantity'] += quant.quantity
            location_stock[location.id]['reserved_quantity'] += quant.reserved_quantity

        # Create stock info lines
        stock_lines = []
        for loc_data in location_stock.values():
            available_qty = loc_data['quantity'] - loc_data['reserved_quantity']
            if available_qty > 0:  # Only show locations with available stock
                stock_lines.append((0, 0, {
                    'location_id': loc_data['location'].id,
                    'on_hand_qty': loc_data['quantity'],
                    'reserved_qty': loc_data['reserved_quantity'],
                    'available_qty': available_qty,
                }))

        self.stock_info_ids = stock_lines

    @api.depends('product_id', 'stock_info_ids')
    def _compute_stock_details_text(self):
        """Compute stock details as text for display"""
        for line in self:
            if not line.product_id:
                line.stock_details_text = "No product selected"
                continue

            if not line.stock_info_ids:
                line.stock_details_text = f"No stock found for {line.product_name}"
                continue

            # Build stock details text
            details = [f"Stock for {line.product_name}:"]
            for stock in line.stock_info_ids:
                details.append(f"• {stock.location_name}: {stock.available_qty} available ({stock.on_hand_qty} on hand, {stock.reserved_qty} reserved)")

            line.stock_details_text = "\n".join(details)

    def write(self, vals):
        """Override write to update source record when is_selected changes"""
        result = super().write(vals)

        # If is_selected field is being updated
        if 'is_selected' in vals and vals['is_selected']:
            # Update the source record with this product
            self._update_source_record()

        return result

    def _update_source_record(self):
        """Update the source record with the selected product"""
        self.ensure_one()

        if not self.is_selected:
            return

        try:
            source_model = self.env[self.wizard_id.source_model]
            source_record = source_model.browse(self.wizard_id.source_record_id)

            if source_record.exists():
                # Update the product_id field
                source_record.write({'product_id': self.product_id.id})

                _logger.info("Auto-updated %s record %s with product %s via wizard save",
                           self.wizard_id.source_model, self.wizard_id.source_record_id, self.product_id.name)

        except Exception as e:
            _logger.error("Error auto-updating source record: %s", str(e))


class ProductSelectionWizardStock(models.TransientModel):
    _name = 'product.selection.wizard.stock'
    _description = 'Product Selection Wizard Stock Information'

    line_id = fields.Many2one('product.selection.wizard.line', string='Line', ondelete='cascade')
    location_id = fields.Many2one('stock.location', string='Location', required=True)
    location_name = fields.Char(related='location_id.complete_name', string='Location', readonly=True)
    on_hand_qty = fields.Float(string='On Hand Qty')
    reserved_qty = fields.Float(string='Reserved Qty')
    available_qty = fields.Float(string='Available Qty')
