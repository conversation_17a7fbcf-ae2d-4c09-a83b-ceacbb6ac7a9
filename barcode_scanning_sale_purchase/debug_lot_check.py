#!/usr/bin/env python3
"""
Debug script to check lot numbers and their associated products.
Run this in Odoo shell to debug the lot number issue.

Usage:
python3 odoo-bin shell -d your_database --addons-path=/path/to/addons
>>> exec(open('/path/to/barcode_scanning_sale_purchase/debug_lot_check.py').read())
"""

def check_lot_number(env, lot_name):
    """Check if a lot number exists and what products are associated with it."""
    print(f"\n=== Checking Lot Number: {lot_name} ===")
    
    # Search for lots with this name
    lots = env['stock.lot'].search([('name', '=', lot_name)])
    print(f"Found {len(lots)} lots with name '{lot_name}'")
    
    if not lots:
        print("No lots found with this name!")
        return
    
    for lot in lots:
        print(f"\nLot ID: {lot.id}")
        print(f"Lot Name: {lot.name}")
        print(f"Product: {lot.product_id.name} (ID: {lot.product_id.id})")
        print(f"Product Code: {lot.product_id.default_code}")
        print(f"Product Barcode: {lot.product_id.barcode}")
        print(f"Company: {lot.company_id.name}")
    
    # Get all product IDs
    product_ids = lots.mapped('product_id.id')
    print(f"\nAll Product IDs for this lot: {product_ids}")
    
    # Test the domain filter logic
    if product_ids:
        domain_filter = str([('id', 'in', product_ids)])
        print(f"Domain filter would be: {domain_filter}")
    else:
        print("No products found, domain filter would be: [(1, '=', 1)]")
    
    return lots

def test_sale_order_line_domain(env, lot_name):
    """Test the sale order line domain filter computation."""
    print(f"\n=== Testing Sale Order Line Domain Filter ===")
    
    # Create a test sale order line
    partner = env['res.partner'].search([('is_company', '=', True)], limit=1)
    if not partner:
        partner = env['res.partner'].create({'name': 'Test Customer'})
    
    sale_order = env['sale.order'].create({
        'partner_id': partner.id,
    })
    
    sale_line = env['sale.order.line'].create({
        'order_id': sale_order.id,
        'product_uom_qty': 1.0,
        'barcode_scan': lot_name,
    })
    
    # Trigger the computation
    sale_line._compute_product_domain_filter()
    
    print(f"Sale line ID: {sale_line.id}")
    print(f"Barcode scan: {sale_line.barcode_scan}")
    print(f"Product domain filter: {sale_line.product_domain_filter}")
    print(f"Selected product: {sale_line.product_id.name if sale_line.product_id else 'None'}")
    
    # Clean up
    sale_order.unlink()

if __name__ == '__main__':
    # This will only work if run in Odoo shell context
    try:
        # Check the specific lot number
        check_lot_number(env, 'A125E16001')
        
        # Test the sale order line domain
        test_sale_order_line_domain(env, 'A125E16001')
        
        # Also check if there are any lots at all
        print(f"\n=== General Lot Statistics ===")
        all_lots = env['stock.lot'].search([])
        print(f"Total lots in database: {len(all_lots)}")
        
        if all_lots:
            print("Sample lot names:")
            for lot in all_lots[:10]:  # Show first 10
                print(f"  - {lot.name} (Product: {lot.product_id.name})")
        
    except NameError:
        print("This script should be run in Odoo shell context where 'env' is available")
        print("Example:")
        print("python3 odoo-bin shell -d your_database")
        print(">>> exec(open('debug_lot_check.py').read())")
