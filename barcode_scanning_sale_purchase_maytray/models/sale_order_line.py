# -*- coding: utf-8 -*-
################################################################################
#
#    Cybrosys Technologies Pvt. Ltd.
#
#    Copyright (C) 2024-TODAY Cybrosys Technologies(<https://www.cybrosys.com>).
#    Author:  <PERSON><PERSON> (<EMAIL>)
#
#    You can modify it under the terms of the GNU AFFERO
#    GENERAL PUBLIC LICENSE (AGPL v3), Version 3.
#
#    This program is distributed in the hope that it will be useful,
#    but WITHOUT ANY WARRANTY; without even the implied warranty of
#    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#    GNU AFFERO GENERAL PUBLIC LICENSE (AGPL v3) for more details.
#
#    You should have received a copy of the GNU AFFERO GENERAL PUBLIC LICENSE
#    (AGPL v3) along with this program.
#    If not, see <http://www.gnu.org/licenses/>.
#
################################################################################
from odoo import api, fields, models


class SaleOrderLine(models.Model):
    """Inherited the model for to add field for barcode."""
    _inherit = 'sale.order.line'

    barcode_scan = fields.Char(string='Lot No',
                               help="Enter the lot number or scan product barcode. "
                                    "The system will find the product while preserving the lot number value.")
    product_domain = fields.Char(compute='_compute_product_domain', store=False,
                                help="Dynamic domain for product_id field based on barcode_scan")
    filtered_product_ids = fields.Char(string="Filtered Product IDs", copy=False,
                                     help="Technical field to store product IDs filtered by lot number")

    @api.depends('barcode_scan')
    def _compute_product_domain(self):
        """Compute the domain for product_id field based on barcode_scan"""
        for line in self:
            if not line.barcode_scan:
                line.product_domain = "[]"
                continue

            # Check if there are products associated with this lot number
            lots = self.env['stock.lot'].search([('name', '=', line.barcode_scan)])
            if lots:
                product_ids = lots.mapped('product_id.id')
                if product_ids:
                    line.product_domain = str(product_ids)
                    continue

            # If no lots found or no products associated with lots, keep default domain
            line.product_domain = "[]"

    @api.onchange('barcode_scan')
    def _onchange_barcode_scan(self):
        """Onchange function for searching product using their barcode or lot/serial number
        while preserving the entered lot number value"""
        if not self.barcode_scan:
            # Clear any domain filtering
            return {'domain': {'product_id': []}}

        # We're keeping the entered value in self.barcode_scan
        # No need to store it in a separate variable

        # First try to find product by barcode
        product = self.env['product.product'].search(
            [('barcode', '=', self.barcode_scan)], limit=1)

        if product:
            self.product_id = product
            # Keep the entered value instead of replacing with product barcode
            return

        # If no product found by barcode, try to find by lot/serial number
        lots = self.env['stock.lot'].search([('name', '=', self.barcode_scan)])

        # Add logging to see what lots are found
        print(f"Found {len(lots)} lots for barcode {self.barcode_scan}")

        if lots:
            products = lots.mapped('product_id')
            product_ids = products.ids

            # Add logging to see what products are found
            print(f"Found {len(products)} products for lots: {products.mapped('name')}")

            # If products found for the lot
            if products:
                # If there's only one product, select it directly
                if len(products) == 1:
                    self.product_id = products[0]
                    # Apply domain filter to show only products associated with this lot
                    return {'domain': {'product_id': [('id', 'in', product_ids)]}}
                # If there are multiple products, pre-select the first one and show a warning
                else:
                    # Set the first product as default using multiple approaches
                    product = products[0]

                    # Try multiple approaches to set the product
                    try:
                        # Approach 1: Direct attribute assignment
                        self.product_id = product

                        # Approach 2: Use write method
                        self.write({'product_id': product.id})

                        # Approach 3: Direct SQL update
                        self.env.cr.execute("""
                            UPDATE sale_order_line
                            SET product_id = %s
                            WHERE id = %s
                        """, (product.id, self.id))

                        # Approach 4: Use sudo to bypass access rights
                        self.sudo().write({'product_id': product.id})

                        # Flush all changes to the database
                        self.env.cr.commit()
                    except Exception as e:
                        # Log the error but continue
                        import logging
                        logger = logging.getLogger(__name__)
                        logger.error("Error setting product: %s", e)

                    # Store the product IDs for later use
                    self.env.cr.execute("""
                        SELECT column_name FROM information_schema.columns
                        WHERE table_name='sale_order_line' AND column_name='filtered_product_ids'
                    """)
                    has_filtered_product_ids = bool(self.env.cr.fetchone())

                    if has_filtered_product_ids:
                        self.filtered_product_ids = str(product_ids)

                    # Return a warning to inform the user that multiple products were found
                    return {
                        'warning': {
                            'title': 'Multiple Products Found',
                            'message': f'Multiple products found for lot {self.barcode_scan}. The first product has been selected automatically. Use the "Select Product" button to choose a different product.'
                        },
                        'domain': {'product_id': [('id', 'in', product_ids)]}
                    }

        # If no products found for the lot, return empty domain
        return {'domain': {'product_id': []}}

    def show_lot_products(self):
        """Show all products associated with the lot number in a popup and allow selection."""
        self.ensure_one()

        # Add explicit logging
        import logging
        logger = logging.getLogger(__name__)
        logger.info("=== show_lot_products called for sale line %s with barcode_scan=%s ===", self.id, self.barcode_scan)

        if not self.barcode_scan:
            logger.warning("No barcode_scan value provided")
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Lot Number',
                    'message': 'Please enter a lot number first.',
                    'type': 'warning',
                }
            }

        # Get products associated with the lot number
        lots = self.env['stock.lot'].search([('name', '=', self.barcode_scan)])
        if not lots:
            logger.warning("No lots found for barcode_scan %s", self.barcode_scan)
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Lots Found',
                    'message': f'No lots found for number {self.barcode_scan}',
                    'type': 'warning',
                }
            }

        product_ids = lots.mapped('product_id.id')
        if not product_ids:
            logger.warning("No products found for lots %s", lots.mapped('name'))
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Products Found',
                    'message': f'No products found for lot number {self.barcode_scan}',
                    'type': 'warning',
                }
            }

        # If there's only one product, just show it
        if len(product_ids) == 1:
            # Set the product directly
            self.product_id = product_ids[0]
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Product Selected',
                    'message': f'Selected product {self.product_id.name} for lot {self.barcode_scan}',
                    'type': 'success',
                }
            }
        # If there are multiple products, open the selection wizard
        else:
            # Create a temporary stock.move record to use with the wizard
            # Use the first product as a default value for the temporary move
            temp_move = self.env['stock.move'].create({
                'name': 'Temporary Move',
                'barcode_scan': self.barcode_scan,
                'product_id': product_ids[0],  # Use the first product as a default
                'product_uom': self.env['product.product'].browse(product_ids[0]).uom_id.id,  # Use the product's UoM
                'product_uom_qty': 0,
                'location_id': 1,  # Dummy location
                'location_dest_id': 1,  # Dummy location
            })

            # Open the product selection wizard
            wizard_action = {
                'name': 'Select Product',
                'type': 'ir.actions.act_window',
                'res_model': 'product.lot.selection.wizard',
                'view_mode': 'form',
                'view_id': self.env.ref('barcode_scanning_sale_purchase.view_product_lot_selection_wizard_form').id,
                'target': 'new',
                'context': {
                    'default_lot_number': self.barcode_scan,
                    'default_stock_move_id': temp_move.id,
                    'default_available_product_ids': [(6, 0, product_ids)],
                    'default_sale_order_line_id': self.id,
                    'default_selected_product_id': product_ids[0],
                    'force_product_update': True,
                }
            }

            # Set the first product as default on the sale order line
            # This ensures that a product is always selected
            product = self.env['product.product'].browse(product_ids[0])

            # Try multiple approaches to set the product
            try:
                # Approach 1: Direct attribute assignment
                logger.info("Approach 1: Direct attribute assignment")
                self.product_id = product

                # Approach 2: Use write method
                logger.info("Approach 2: Using write method")
                self.write({'product_id': product.id})

                # Approach 3: Direct SQL update
                logger.info("Approach 3: Direct SQL update")
                self.env.cr.execute("""
                    UPDATE sale_order_line
                    SET product_id = %s
                    WHERE id = %s
                """, (product.id, self.id))

                # Approach 4: Use sudo to bypass access rights
                logger.info("Approach 4: Using sudo")
                self.sudo().write({'product_id': product.id})

                # Approach 5: Use a different method to update
                logger.info("Approach 5: Using a different method to update")
                try:
                    # Try to update using ORM methods first
                    self.env['sale.order.line'].browse(self.id).product_id = product
                except Exception as e2:
                    logger.error("Error in approach 5: %s", e2)

                # Verify the update without committing
                try:
                    current_product = self.env['sale.order.line'].browse(self.id).product_id
                    logger.info("Current product after all approaches: %s (ID: %s)",
                               current_product.name if current_product else "None",
                               current_product.id if current_product else None)
                except Exception as e3:
                    logger.error("Error verifying product: %s", e3)

                # Log the result
                logger.info("Product set to %s (ID: %s) on sale order line %s",
                           product.name, product.id, self.id)
            except Exception as e:
                logger.error("Error setting product: %s", e)

            # Enhanced logging for the wizard action
            logger.info("Opening product selection wizard with context: %s", wizard_action['context'])
            logger.info("Wizard view ID: %s", wizard_action['view_id'])
            logger.info("Full wizard action: %s", wizard_action)

            # Return the wizard action
            return wizard_action
