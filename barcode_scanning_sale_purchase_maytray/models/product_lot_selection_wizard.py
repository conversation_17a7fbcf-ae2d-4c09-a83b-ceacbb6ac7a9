# -*- coding: utf-8 -*-
################################################################################
#
#    Cybrosys Technologies Pvt. Ltd.
#
#    Copyright (C) 2024-TODAY Cybrosys Technologies(<https://www.cybrosys.com>).
#    Author:  <PERSON><PERSON> (<EMAIL>)
#
#    You can modify it under the terms of the GNU AFFERO
#    GENERAL PUBLIC LICENSE (AGPL v3), Version 3.
#
#    This program is distributed in the hope that it will be useful,
#    but WITHOUT ANY WARRANTY; without even the implied warranty of
#    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#
#    You should have received a copy of the GNU AFFERO GENERAL PUBLIC LICENSE
#    (AGPL v3) along with this program.
#    If not, see <http://www.gnu.org/licenses/>.
#
################################################################################
from odoo import api, fields, models
import logging

logger = logging.getLogger(__name__)


class ProductLotSelectionWizard(models.TransientModel):
    """Wizard for selecting a product when multiple products are found for a lot number."""
    _name = 'product.lot.selection.wizard'
    _description = 'Product Selection Wizard for Lot Numbers'

    lot_number = fields.Char(string='Lot Number', readonly=True)
    stock_move_id = fields.Many2one('stock.move', string='Stock Move', readonly=True)
    sale_order_line_id = fields.Many2one('sale.order.line', string='Sale Order Line', readonly=True)
    purchase_order_line_id = fields.Many2one('purchase.order.line', string='Purchase Order Line', readonly=True)
    account_move_line_id = fields.Many2one('account.move.line', string='Invoice Line', readonly=True)
    available_product_ids = fields.Many2many('product.product', string='Available Products')
    selected_product_id = fields.Many2one('product.product', string='Selected Product', required=True)
    force_product_update = fields.Boolean(string='Force Product Update', default=False,
                                        help="If True, force the product update even if there are restrictions")

    @api.onchange('available_product_ids')
    def _onchange_available_product_ids(self):
        """Set domain for selected_product_id based on available_product_ids."""
        if self.available_product_ids:
            # Extract product IDs from the available_product_ids
            product_ids = self.available_product_ids.ids
            return {'domain': {'selected_product_id': [('id', 'in', product_ids)]}}
        return {'domain': {'selected_product_id': []}}

    @api.model
    def default_get(self, fields_list):
        """Set default values from the context."""
        logger.info("ProductLotSelectionWizard.default_get called with fields_list: %s", fields_list)
        logger.info("Context: %s", self._context)

        res = super(ProductLotSelectionWizard, self).default_get(fields_list)
        logger.info("Result from super().default_get: %s", res)

        # Get values from context
        lot_number = self._context.get('default_lot_number')
        stock_move_id = self._context.get('default_stock_move_id')
        sale_order_line_id = self._context.get('default_sale_order_line_id')
        purchase_order_line_id = self._context.get('default_purchase_order_line_id')
        account_move_line_id = self._context.get('default_account_move_line_id')
        available_product_ids = self._context.get('default_available_product_ids')
        selected_product_id = self._context.get('default_selected_product_id')
        force_product_update = self._context.get('force_product_update', False)

        # Log all context keys for debugging
        logger.info("All context keys: %s", list(self._context.keys()))

        logger.info("Got values from context: lot_number=%s, stock_move_id=%s, sale_order_line_id=%s, purchase_order_line_id=%s, account_move_line_id=%s",
                    lot_number, stock_move_id, sale_order_line_id, purchase_order_line_id, account_move_line_id)
        logger.info("Available product IDs from context: %s", available_product_ids)
        logger.info("Force product update: %s", force_product_update)

        # Set values in the wizard
        if lot_number:
            res['lot_number'] = lot_number

        if stock_move_id:
            res['stock_move_id'] = stock_move_id

        if sale_order_line_id:
            res['sale_order_line_id'] = sale_order_line_id

        if purchase_order_line_id:
            res['purchase_order_line_id'] = purchase_order_line_id

        if account_move_line_id:
            res['account_move_line_id'] = account_move_line_id

        # Set force_product_update if it's in the context
        if force_product_update:
            res['force_product_update'] = True
            logger.info("Setting force_product_update to True")

        if available_product_ids:
            # Make sure available_product_ids is properly set
            if isinstance(available_product_ids, list) and available_product_ids[0][0] == 6:
                product_ids = available_product_ids[0][2]
                logger.info("Product IDs extracted: %s", product_ids)
                logger.info("User will need to select one of these products")

                # Set available_product_ids directly as a list of IDs
                res['available_product_ids'] = [(6, 0, product_ids)]

                # Use the selected_product_id from context if provided, otherwise use the first product
                if selected_product_id:
                    res['selected_product_id'] = selected_product_id
                    logger.info("Using selected product from context: %s", selected_product_id)
                # Automatically select the first product as default if no specific product is selected
                elif product_ids and len(product_ids) > 0:
                    res['selected_product_id'] = product_ids[0]
                    logger.info("Auto-selected first product: %s", product_ids[0])
            else:
                logger.info("available_product_ids is not in expected format: %s", available_product_ids)
                res['available_product_ids'] = available_product_ids

        logger.info("Final result from default_get: %s", res)
        return res

    def action_select_product(self):
        """Apply the selected product to the appropriate model."""
        self.ensure_one()
        logger.info("action_select_product called on wizard %s", self)
        logger.info("Selected product: %s", self.selected_product_id)
        logger.info("Force product update: %s", self.force_product_update)

        if not self.selected_product_id:
            logger.warning("No product selected in the wizard")
            # Return a warning if no product is selected
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Product Selected',
                    'message': 'Please select a product before continuing.',
                    'type': 'warning',
                    'sticky': False,
                }
            }

        # Handle stock move
        if self.stock_move_id:
            # Check if the product has changed
            if self.stock_move_id.product_id.id != self.selected_product_id.id:
                logger.info("Changing product on stock_move from %s to %s",
                           self.stock_move_id.product_id.id, self.selected_product_id.id)
                # Set the selected product on the stock move
                self.stock_move_id.product_id = self.selected_product_id
                # Update the product UoM to match the selected product
                self.stock_move_id.product_uom = self.selected_product_id.uom_id.id
            else:
                logger.info("Product already set to %s on stock_move, no change needed",
                           self.selected_product_id.id)

            # If this is a move line that can have a lot assigned, assign it
            if hasattr(self.stock_move_id, 'move_line_ids') and self.stock_move_id.move_line_ids:
                lots = self.env['stock.lot'].search([('name', '=', self.lot_number)])
                matching_lot = lots.filtered(lambda l: l.product_id.id == self.selected_product_id.id)
                if matching_lot:
                    for move_line in self.stock_move_id.move_line_ids:
                        if not move_line.lot_id and move_line.product_id.id == self.selected_product_id.id:
                            move_line.lot_id = matching_lot[0]
                            break

        # Handle sale order line
        elif self.sale_order_line_id:
            logger.info("Handling sale_order_line_id=%s", self.sale_order_line_id)

            # First, try to get the sale order line
            try:
                # Get the sale order line ID
                sale_line_id = int(self.sale_order_line_id)
                logger.info("Sale order line ID: %s", sale_line_id)

                # Get the sale order line record
                sale_line = self.env['sale.order.line'].browse(sale_line_id)
                logger.info("Sale line exists: %s", sale_line.exists())

                if not sale_line.exists():
                    logger.warning("Sale order line %s does not exist", sale_line_id)
                    return {
                        'type': 'ir.actions.client',
                        'tag': 'display_notification',
                        'params': {
                            'title': 'Error',
                            'message': f'Sale order line {sale_line_id} not found',
                            'type': 'warning',
                            'sticky': True,
                        }
                    }

                # Log the current product
                logger.info("Current product on sale_order_line: %s", sale_line.product_id.id if sale_line.product_id else None)

                # Get the product ID
                product_id = self.selected_product_id.id
                logger.info("Selected product ID: %s", product_id)

                # Try multiple approaches to update the product

                # Approach 1: Direct SQL update (without commit)
                logger.info("Approach 1: Direct SQL update")
                try:
                    self.env.cr.execute("""
                        UPDATE sale_order_line
                        SET product_id = %s
                        WHERE id = %s
                    """, (product_id, sale_line_id))
                except Exception as e1:
                    logger.error("Error in approach 1: %s", e1)

                # Approach 2: Use write method
                logger.info("Approach 2: Using write method")
                try:
                    sale_line.write({'product_id': product_id})
                except Exception as e2:
                    logger.error("Error in approach 2: %s", e2)

                # Approach 3: Direct attribute assignment
                logger.info("Approach 3: Direct attribute assignment")
                try:
                    sale_line.product_id = self.selected_product_id
                except Exception as e3:
                    logger.error("Error in approach 3: %s", e3)

                # Approach 4: Use sudo to bypass access rights
                logger.info("Approach 4: Using sudo")
                try:
                    sale_line.sudo().write({'product_id': product_id})
                except Exception as e4:
                    logger.error("Error in approach 4: %s", e4)

                # Reload the record to reflect the changes
                sale_line.invalidate_cache()

                # Reload the record again
                sale_line = self.env['sale.order.line'].browse(sale_line_id)

                # Log the updated product
                logger.info("Updated product on sale_order_line to: %s", sale_line.product_id.id if sale_line.product_id else None)

                # Update product UoM if needed
                if hasattr(sale_line, 'product_uom') and self.selected_product_id.uom_id:
                    try:
                        sale_line.product_uom = self.selected_product_id.uom_id.id
                        logger.info("Updated product_uom on sale_order_line to: %s", self.selected_product_id.uom_id.id)
                    except Exception as e5:
                        logger.error("Error updating product_uom: %s", e5)

                # Final verification - check if the product was actually updated
                sale_line = self.env['sale.order.line'].browse(sale_line_id)
                if sale_line.product_id.id != product_id:
                    logger.warning("Product still not updated after all attempts. Trying one more approach.")

                    # Try one more approach
                    try:
                        # Try a different method to update the product
                        logger.info("Final approach: Using a different method")
                        self.env['sale.order.line'].browse(sale_line_id).product_id = self.selected_product_id

                        # Reload the record
                        sale_line.invalidate_cache()
                        sale_line = self.env['sale.order.line'].browse(sale_line_id)

                        logger.info("Final product check: %s", sale_line.product_id.id)
                    except Exception as e6:
                        logger.error("Error in final update attempt: %s", e6)

            except Exception as e:
                logger.error("Error updating sale order line: %s", e)
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': 'Error',
                        'message': f'Error updating sale order line: {e}',
                        'type': 'warning',
                        'sticky': True,
                    }
                }

        # Handle purchase order line
        elif self.purchase_order_line_id:
            logger.info("Handling purchase_order_line_id=%s", self.purchase_order_line_id)

            # First, try to get the purchase order line
            try:
                # Get the purchase order line ID
                purchase_line_id = int(self.purchase_order_line_id)
                logger.info("Purchase order line ID: %s", purchase_line_id)

                # Get the purchase order line record
                purchase_line = self.env['purchase.order.line'].browse(purchase_line_id)
                logger.info("Purchase line exists: %s", purchase_line.exists())

                if not purchase_line.exists():
                    logger.warning("Purchase order line %s does not exist", purchase_line_id)
                    return {
                        'type': 'ir.actions.client',
                        'tag': 'display_notification',
                        'params': {
                            'title': 'Error',
                            'message': f'Purchase order line {purchase_line_id} not found',
                            'type': 'warning',
                            'sticky': True,
                        }
                    }

                # Log the current product
                logger.info("Current product on purchase_order_line: %s", purchase_line.product_id.id if purchase_line.product_id else None)

                # Get the product ID
                product_id = self.selected_product_id.id
                logger.info("Selected product ID: %s", product_id)

                # Try multiple approaches to update the product

                # Approach 1: Direct SQL update (without commit)
                logger.info("Approach 1: Direct SQL update")
                try:
                    self.env.cr.execute("""
                        UPDATE purchase_order_line
                        SET product_id = %s
                        WHERE id = %s
                    """, (product_id, purchase_line_id))
                except Exception as e1:
                    logger.error("Error in approach 1: %s", e1)

                # Approach 2: Use write method
                logger.info("Approach 2: Using write method")
                try:
                    purchase_line.write({'product_id': product_id})
                except Exception as e2:
                    logger.error("Error in approach 2: %s", e2)

                # Approach 3: Direct attribute assignment
                logger.info("Approach 3: Direct attribute assignment")
                try:
                    purchase_line.product_id = self.selected_product_id
                except Exception as e3:
                    logger.error("Error in approach 3: %s", e3)

                # Approach 4: Use sudo to bypass access rights
                logger.info("Approach 4: Using sudo")
                try:
                    purchase_line.sudo().write({'product_id': product_id})
                except Exception as e4:
                    logger.error("Error in approach 4: %s", e4)

                # Reload the record to reflect the changes
                purchase_line.invalidate_cache()

                # Reload the record again
                purchase_line = self.env['purchase.order.line'].browse(purchase_line_id)

                # Log the updated product
                logger.info("Updated product on purchase_order_line to: %s", purchase_line.product_id.id if purchase_line.product_id else None)

                # Update product UoM if needed
                if hasattr(purchase_line, 'product_uom') and self.selected_product_id.uom_id:
                    try:
                        purchase_line.product_uom = self.selected_product_id.uom_id.id
                        logger.info("Updated product_uom on purchase_order_line to: %s", self.selected_product_id.uom_id.id)
                    except Exception as e5:
                        logger.error("Error updating product_uom: %s", e5)

                # Final verification - check if the product was actually updated
                purchase_line = self.env['purchase.order.line'].browse(purchase_line_id)
                if purchase_line.product_id.id != product_id:
                    logger.warning("Product still not updated after all attempts. Trying one more approach.")

                    # Try one more approach
                    try:
                        # Try a different method to update the product
                        logger.info("Final approach: Using a different method")
                        self.env['purchase.order.line'].browse(purchase_line_id).product_id = self.selected_product_id

                        # Reload the record
                        purchase_line.invalidate_cache()
                        purchase_line = self.env['purchase.order.line'].browse(purchase_line_id)

                        logger.info("Final product check: %s", purchase_line.product_id.id)
                    except Exception as e6:
                        logger.error("Error in final update attempt: %s", e6)

            except Exception as e:
                logger.error("Error updating purchase order line: %s", e)
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': 'Error',
                        'message': f'Error updating purchase order line: {e}',
                        'type': 'warning',
                        'sticky': True,
                    }
                }

        # Handle account move line
        elif self.account_move_line_id:
            logger.info("Handling account_move_line_id=%s", self.account_move_line_id)

            # First, try to get the account move line
            try:
                # Get the account move line ID
                move_line_id = int(self.account_move_line_id)
                logger.info("Account move line ID: %s", move_line_id)

                # Get the account move line record
                move_line = self.env['account.move.line'].browse(move_line_id)
                logger.info("Account move line exists: %s", move_line.exists())

                if not move_line.exists():
                    logger.warning("Account move line %s does not exist", move_line_id)
                    return {
                        'type': 'ir.actions.client',
                        'tag': 'display_notification',
                        'params': {
                            'title': 'Error',
                            'message': f'Account move line {move_line_id} not found',
                            'type': 'warning',
                            'sticky': True,
                        }
                    }

                # Log the current product
                logger.info("Current product on account_move_line: %s", move_line.product_id.id if move_line.product_id else None)

                # Get the product ID
                product_id = self.selected_product_id.id
                logger.info("Selected product ID: %s", product_id)

                # Try multiple approaches to update the product

                # Approach 1: Direct SQL update (without commit)
                logger.info("Approach 1: Direct SQL update")
                try:
                    self.env.cr.execute("""
                        UPDATE account_move_line
                        SET product_id = %s
                        WHERE id = %s
                    """, (product_id, move_line_id))
                except Exception as e1:
                    logger.error("Error in approach 1: %s", e1)

                # Approach 2: Use write method
                logger.info("Approach 2: Using write method")
                try:
                    move_line.write({'product_id': product_id})
                except Exception as e2:
                    logger.error("Error in approach 2: %s", e2)

                # Approach 3: Direct attribute assignment
                logger.info("Approach 3: Direct attribute assignment")
                try:
                    move_line.product_id = self.selected_product_id
                except Exception as e3:
                    logger.error("Error in approach 3: %s", e3)

                # Approach 4: Use sudo to bypass access rights
                logger.info("Approach 4: Using sudo")
                try:
                    move_line.sudo().write({'product_id': product_id})
                except Exception as e4:
                    logger.error("Error in approach 4: %s", e4)

                # Reload the record to reflect the changes
                move_line.invalidate_cache()

                # Reload the record again
                move_line = self.env['account.move.line'].browse(move_line_id)

                # Log the updated product
                logger.info("Updated product on account_move_line to: %s", move_line.product_id.id if move_line.product_id else None)

                # Final verification - check if the product was actually updated
                move_line = self.env['account.move.line'].browse(move_line_id)
                if move_line.product_id.id != product_id:
                    logger.warning("Product still not updated after all attempts. Trying one more approach.")

                    # Try one more approach
                    try:
                        # Try a different method to update the product
                        logger.info("Final approach: Using a different method")
                        self.env['account.move.line'].browse(move_line_id).product_id = self.selected_product_id

                        # Reload the record
                        move_line.invalidate_cache()
                        move_line = self.env['account.move.line'].browse(move_line_id)

                        logger.info("Final product check: %s", move_line.product_id.id)
                    except Exception as e6:
                        logger.error("Error in final update attempt: %s", e6)

            except Exception as e:
                logger.error("Error updating account move line: %s", e)
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': 'Error',
                        'message': f'Error updating account move line: {e}',
                        'type': 'warning',
                        'sticky': True,
                    }
                }

        # Clean up temporary stock move if it was created just for the wizard
        if self.stock_move_id and self.stock_move_id.name == 'Temporary Move':
            try:
                # Try to delete the temporary move, but don't fail if it can't be deleted
                self.stock_move_id.unlink()
            except Exception as e:
                # If we can't delete it, just ignore the error
                logger.warning("Failed to delete temporary move: %s", e)
                pass

        # Prepare a detailed message about what was updated
        message = f'Selected product {self.selected_product_id.name} for lot {self.lot_number}.\n\n'

        success = True
        update_status = "✅ Success"

        if self.sale_order_line_id:
            sale_line = self.env['sale.order.line'].browse(int(self.sale_order_line_id))
            if sale_line.exists():
                message += f'Updated Sale Order Line: {sale_line.order_id.name} - Line {sale_line.id}\n'

                # Check if the product was actually updated
                if sale_line.product_id.id == self.selected_product_id.id:
                    message += f'Current product on line: {sale_line.product_id.name} ✅\n'
                else:
                    message += f'Current product on line: {sale_line.product_id.name} ❌ (NOT UPDATED)\n'
                    message += f'Expected product: {self.selected_product_id.name}\n'
                    success = False
                    update_status = "❌ Failed"

        elif self.purchase_order_line_id:
            purchase_line = self.env['purchase.order.line'].browse(int(self.purchase_order_line_id))
            if purchase_line.exists():
                message += f'Updated Purchase Order Line: {purchase_line.order_id.name} - Line {purchase_line.id}\n'

                # Check if the product was actually updated
                if purchase_line.product_id.id == self.selected_product_id.id:
                    message += f'Current product on line: {purchase_line.product_id.name} ✅\n'
                else:
                    message += f'Current product on line: {purchase_line.product_id.name} ❌ (NOT UPDATED)\n'
                    message += f'Expected product: {self.selected_product_id.name}\n'
                    success = False
                    update_status = "❌ Failed"

        elif self.account_move_line_id:
            move_line = self.env['account.move.line'].browse(int(self.account_move_line_id))
            if move_line.exists():
                message += f'Updated Invoice Line: {move_line.move_id.name} - Line {move_line.id}\n'

                # Check if the product was actually updated
                if move_line.product_id.id == self.selected_product_id.id:
                    message += f'Current product on line: {move_line.product_id.name} ✅\n'
                else:
                    message += f'Current product on line: {move_line.product_id.name} ❌ (NOT UPDATED)\n'
                    message += f'Expected product: {self.selected_product_id.name}\n'
                    success = False
                    update_status = "❌ Failed"

        elif self.stock_move_id:
            message += f'Updated Stock Move: {self.stock_move_id.name} - ID {self.stock_move_id.id}\n'

            # Check if the product was actually updated
            if self.stock_move_id.product_id.id == self.selected_product_id.id:
                message += f'Current product on move: {self.stock_move_id.product_id.name} ✅\n'
            else:
                message += f'Current product on move: {self.stock_move_id.product_id.name} ❌ (NOT UPDATED)\n'
                message += f'Expected product: {self.selected_product_id.name}\n'
                success = False
                update_status = "❌ Failed"

        # Add a summary line at the top
        message = f'Product Update {update_status}\n\n' + message

        # Add troubleshooting info if the update failed
        if not success:
            message += '\n\nTroubleshooting:\n'
            message += '1. Try refreshing the page\n'
            message += '2. Check if there are any constraints preventing the product change\n'
            message += '3. Contact your system administrator if the issue persists\n'

        # Return a notification with detailed information
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Product Selected' if success else 'Product Update Failed',
                'message': message,
                'type': 'success' if success else 'warning',
                'next': {'type': 'ir.actions.act_window_close'},
                'sticky': True,  # Make it sticky so the user can read the details
            }
        }