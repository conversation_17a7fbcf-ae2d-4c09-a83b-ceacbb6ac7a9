<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <!-- Form view for product lot selection wizard -->
    <record id="view_product_lot_selection_wizard_form" model="ir.ui.view">
        <field name="name">product.lot.selection.wizard.form</field>
        <field name="model">product.lot.selection.wizard</field>
        <field name="arch" type="xml">
            <form string="Select Product">
                <div class="alert alert-info" role="alert">
                    <p><strong>Multiple products found for this lot number.</strong></p>
                    <p>Please select which product you want to add.</p>
                </div>
                <sheet>
                    <group>
                        <field name="lot_number" readonly="1"/>
                        <field name="stock_move_id" invisible="1"/>
                        <field name="sale_order_line_id" invisible="1"/>
                        <field name="purchase_order_line_id" invisible="1"/>
                        <field name="account_move_line_id" invisible="1"/>
                        <field name="force_product_update" invisible="1"/>
                        <!-- Removed duplicate field -->
                    </group>
                    <group>
                        <field name="selected_product_id" options="{'no_create': True, 'no_open': False}" placeholder="Select a product..." required="1" domain="[('id', 'in', available_product_ids)]"/>
                    </group>
                    <notebook>
                        <page string="Available Products">
                            <field name="available_product_ids" readonly="1">
                                <list>
                                    <field name="default_code"/>
                                    <field name="name"/>
                                    <field name="barcode"/>
                                    <field name="list_price"/>
                                    <field name="qty_available"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <footer>
                    <button name="action_select_product" string="Confirm Selection" type="object" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Action for opening the wizard -->
    <record id="action_product_lot_selection_wizard" model="ir.actions.act_window">
        <field name="name">Select Product</field>
        <field name="res_model">product.lot.selection.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
</odoo>
