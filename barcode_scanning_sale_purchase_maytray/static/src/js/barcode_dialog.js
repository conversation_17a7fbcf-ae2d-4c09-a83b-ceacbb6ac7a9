odoo.define('barcode_scanning_sale_purchase.barcode_dialog', function (require) {
    "use strict";

    var core = require('web.core');
    var Dialog = require('web.Dialog');
    var ListController = require('web.ListController');
    var _t = core._t;

    // Extend the ListController to add our custom behavior
    ListController.include({
        /**
         * @override
         */
        _onFieldChanged: function (event) {
            var self = this;
            var fieldName = event.target.name;
            
            // Check if the changed field is barcode_scan
            if (fieldName === 'barcode_scan') {
                var record = this.model.get(event.data.dataPointID);
                var barcodeValue = event.data.changes[fieldName];
                
                if (barcodeValue) {
                    // Call the server to get products for this barcode/lot
                    this._rpc({
                        model: 'stock.move',
                        method: 'get_products_by_lot',
                        args: [[record.res_id], barcodeValue],
                    }).then(function (productIds) {
                        if (productIds && productIds.length > 1) {
                            // If multiple products found, show selection dialog
                            self._showProductSelectionDialog(productIds, barcodeValue, record);
                            return;
                        }
                    });
                }
            }
            
            // Call the original method
            this._super.apply(this, arguments);
        },
        
        /**
         * Show a dialog to select a product when multiple products are found
         */
        _showProductSelectionDialog: function (productIds, lotNumber, record) {
            var self = this;
            
            // Get product details
            this._rpc({
                model: 'product.product',
                method: 'search_read',
                args: [[['id', 'in', productIds]], ['id', 'name', 'default_code', 'barcode', 'list_price', 'qty_available']],
            }).then(function (products) {
                var $content = $('<div>').addClass('o_product_selection');
                $content.append($('<p>').text(_t('Multiple products found for lot number: ') + lotNumber));
                
                var $table = $('<table>').addClass('table table-sm');
                var $header = $('<thead>').append($('<tr>')
                    .append($('<th>').text(_t('Code')))
                    .append($('<th>').text(_t('Name')))
                    .append($('<th>').text(_t('Barcode')))
                    .append($('<th>').text(_t('Price')))
                    .append($('<th>').text(_t('Quantity')))
                    .append($('<th>').text(_t('Select')))
                );
                $table.append($header);
                
                var $body = $('<tbody>');
                _.each(products, function (product) {
                    var $row = $('<tr>');
                    $row.append($('<td>').text(product.default_code || ''));
                    $row.append($('<td>').text(product.name || ''));
                    $row.append($('<td>').text(product.barcode || ''));
                    $row.append($('<td>').text(product.list_price || ''));
                    $row.append($('<td>').text(product.qty_available || ''));
                    
                    var $selectButton = $('<button>')
                        .addClass('btn btn-sm btn-primary')
                        .text(_t('Select'))
                        .on('click', function () {
                            self._selectProduct(product.id, record);
                            dialog.close();
                        });
                    
                    $row.append($('<td>').append($selectButton));
                    $body.append($row);
                });
                
                $table.append($body);
                $content.append($table);
                
                var dialog = new Dialog(self, {
                    title: _t('Select Product'),
                    $content: $content,
                    buttons: [{
                        text: _t('Cancel'),
                        close: true
                    }],
                    size: 'medium',
                });
                
                dialog.open();
            });
        },
        
        /**
         * Set the selected product on the record
         */
        _selectProduct: function (productId, record) {
            this.model.notifyChanges(record.id, {product_id: {id: productId}});
            this.update({}, {reload: false});
        },
    });
});
