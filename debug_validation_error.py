#!/usr/bin/env python3
"""
Debug script to help identify the source of the validation error:
"You cannot add the same product VARIYALI READY DIAMOND 1% [30KG] with the same location 'Stock'"

This script provides methods to investigate the issue.
"""

import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def debug_validation_error():
    """
    Debug steps to identify the validation error source
    """
    print("=== DEBUGGING VALIDATION ERROR ===")
    print("Error: You cannot add the same product VARIYALI READY DIAMOND 1% [30KG] with the same location 'Stock'")
    print()
    
    print("STEP 1: Check if this is a standard Odoo constraint")
    print("- Look for SQL constraints in stock.quant model")
    print("- Check for unique constraints on (product_id, location_id)")
    print()
    
    print("STEP 2: Check for custom constraints in your modules")
    print("- Search for _sql_constraints in all models")
    print("- Look for @api.constrains decorators")
    print()
    
    print("STEP 3: Identify the operation causing the error")
    print("- Are you doing inventory adjustments?")
    print("- Are you creating stock moves?")
    print("- Are you importing data?")
    print()
    
    print("STEP 4: Check existing records")
    print("- Look for existing stock.quant records with same product and location")
    print("- Check for pending stock moves")
    print()

def check_existing_records_query():
    """
    SQL queries to check for existing records
    """
    print("=== SQL QUERIES TO RUN IN ODOO SHELL ===")
    print()
    
    print("1. Check existing stock quants for the product:")
    print("""
# In Odoo shell:
product = env['product.product'].search([('name', '=', 'VARIYALI READY DIAMOND 1% [30KG]')])
if product:
    print(f"Product ID: {product.id}")
    quants = env['stock.quant'].search([('product_id', '=', product.id)])
    for quant in quants:
        print(f"Location: {quant.location_id.name}, Quantity: {quant.quantity}, Reserved: {quant.reserved_quantity}")
else:
    print("Product not found")
    """)
    
    print("\n2. Check for pending stock moves:")
    print("""
# In Odoo shell:
moves = env['stock.move'].search([
    ('product_id', '=', product.id),
    ('state', 'not in', ['done', 'cancel'])
])
for move in moves:
    print(f"Move: {move.name}, State: {move.state}, From: {move.location_id.name}, To: {move.location_dest_id.name}")
    """)
    
    print("\n3. Check for inventory adjustments:")
    print("""
# In Odoo shell:
# For Odoo 14+
adjustments = env['stock.quant'].search([
    ('product_id', '=', product.id),
    ('inventory_quantity_set', '=', True)
])
for adj in adjustments:
    print(f"Adjustment in {adj.location_id.name}: Current={adj.quantity}, Set to={adj.inventory_quantity}")
    """)

def potential_solutions():
    """
    List potential solutions based on common causes
    """
    print("=== POTENTIAL SOLUTIONS ===")
    print()
    
    print("SOLUTION 1: If there's an existing quant, update it instead of creating new")
    print("- Use stock.quant._update_available_quantity() method")
    print("- Or use inventory adjustment to modify existing quantity")
    print()
    
    print("SOLUTION 2: If it's a constraint issue, check the operation context")
    print("- Some operations require specific context or sequence")
    print("- Ensure you're not trying to create duplicate inventory lines")
    print()
    
    print("SOLUTION 3: If it's an import/data issue")
    print("- Check for duplicate lines in your data")
    print("- Use 'upsert' logic instead of direct creation")
    print()
    
    print("SOLUTION 4: If it's a custom constraint")
    print("- Review the constraint logic")
    print("- Consider if the constraint is too restrictive")
    print("- Add exception handling if needed")

def create_investigation_script():
    """
    Create a script to run in Odoo shell for investigation
    """
    script = '''
# Run this in Odoo shell to investigate the issue

# 1. Find the product
product_name = 'VARIYALI READY DIAMOND 1% [30KG]'
product = env['product.product'].search([('name', '=', product_name)])

if not product:
    print(f"Product '{product_name}' not found")
    exit()

print(f"Found product: {product.name} (ID: {product.id})")

# 2. Find the Stock location
stock_location = env['stock.location'].search([('name', '=', 'Stock')])
if not stock_location:
    print("Stock location not found")
    exit()

print(f"Found location: {stock_location.name} (ID: {stock_location.id})")

# 3. Check existing quants
existing_quants = env['stock.quant'].search([
    ('product_id', '=', product.id),
    ('location_id', '=', stock_location.id)
])

print(f"\\nExisting quants for this product-location combination: {len(existing_quants)}")
for quant in existing_quants:
    print(f"  Quant ID: {quant.id}, Quantity: {quant.quantity}, Reserved: {quant.reserved_quantity}")
    if quant.lot_id:
        print(f"    Lot: {quant.lot_id.name}")

# 4. Check pending moves
pending_moves = env['stock.move'].search([
    ('product_id', '=', product.id),
    ('location_dest_id', '=', stock_location.id),
    ('state', 'not in', ['done', 'cancel'])
])

print(f"\\nPending moves to this location: {len(pending_moves)}")
for move in pending_moves:
    print(f"  Move: {move.name}, State: {move.state}, Qty: {move.product_uom_qty}")

# 5. Check for any constraints on stock.quant
quant_model = env['stock.quant']
if hasattr(quant_model, '_sql_constraints'):
    print(f"\\nSQL constraints on stock.quant:")
    for constraint in quant_model._sql_constraints:
        print(f"  {constraint}")

# 6. Try to identify what operation is failing
print("\\n=== INVESTIGATION COMPLETE ===")
print("If you're getting the error, it's likely because:")
print("1. There's already a quant for this product-location combination")
print("2. You're trying to create a duplicate inventory adjustment")
print("3. There's a custom constraint preventing the operation")
'''
    
    return script

if __name__ == "__main__":
    debug_validation_error()
    print()
    check_existing_records_query()
    print()
    potential_solutions()
    print()
    print("=== ODOO SHELL INVESTIGATION SCRIPT ===")
    print(create_investigation_script())
