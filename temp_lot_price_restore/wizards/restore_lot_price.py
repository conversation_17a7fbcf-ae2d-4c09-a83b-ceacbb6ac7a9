# -*- coding: utf-8 -*-
import logging
from odoo import models, fields, api, _
from odoo.exceptions import UserError
from datetime import datetime, timedelta
from odoo.tools import float_is_zero, float_compare

_logger = logging.getLogger(__name__)

class RestoreLotPriceWizard(models.TransientModel):
    _name = 'restore.lot.price.wizard'
    _description = 'Restore Lot Price Wizard'

    date_from = fields.Date(string='Date From', default=lambda self: fields.Date.today() - timedelta(days=30))
    date_to = fields.Date(string='Date To', default=lambda self: fields.Date.today())
    lot_ids = fields.Many2many('stock.lot', string='Specific Lots',
                              help="If specified, only these lots will be processed. Leave empty to process all lots.")
    product_ids = fields.Many2many('product.product', string='Specific Products',
                                  help="If specified, only lots of these products will be processed. Leave empty to process all products.")
    include_zero_cost_lots = fields.Boolean(string='Include Zero Cost Lots', default=False,
                                           help="If checked, lots with zero cost will also be processed.")
    only_zero_cost_lots = fields.Boolean(string='Only Zero Cost Lots', default=True,
                                        help="If checked, only lots with zero cost will be processed.")
    # We're removing the update_method selection since we'll always use the most accurate method
    # This field is kept for backward compatibility but hidden in the UI
    update_method = fields.Selection([
        ('auto', 'Automatic (Most Accurate Method)'),
    ], string='Update Method', default='auto', required=True,
       help="The system will automatically determine the most accurate method to restore lot prices.")

    preview_only = fields.Boolean(string='Preview Only', default=True,
                                 help="If checked, no changes will be made to the database. Use this to preview the changes.")

    exclude_manual_modifications = fields.Boolean(
        string='Exclude Manual Modifications',
        default=False,
        help="If checked, valuation layers with 'Lot value manually modified' description will be excluded"
    )

    receipt_pickings_only = fields.Boolean(
        string='Only Receipt Pickings',
        default=True,
        help="If checked, only lots from receipt operations will be processed"
    )

    auto_schedule = fields.Boolean(
        string='Schedule Automatic Runs',
        default=False,
        help="If checked, a scheduled action will be created to run this process automatically"
    )

    schedule_interval = fields.Selection([
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly')
    ], string='Schedule Interval', default='daily')

    log_details = fields.Text(string='Log Details', readonly=True)

    # Statistics fields
    total_lots = fields.Integer(string='Total Lots', readonly=True)
    lots_to_update = fields.Integer(string='Lots to Update', readonly=True)
    lots_with_zero_cost = fields.Integer(string='Lots with Zero Cost', readonly=True)
    lots_with_valuation = fields.Integer(string='Lots with Valuation Layers', readonly=True)

    @api.onchange('date_from', 'date_to', 'lot_ids', 'product_ids', 'include_zero_cost_lots', 'only_zero_cost_lots')
    def _compute_statistics(self):
        """
        Compute statistics about lots and valuation layers
        """
        for wizard in self:
            # Build domain for lots
            lot_domain = []
            if wizard.lot_ids:
                lot_domain.append(('id', 'in', wizard.lot_ids.ids))
            if wizard.product_ids:
                lot_domain.append(('product_id', 'in', wizard.product_ids.ids))

            # Get all lots
            all_lots = self.env['stock.lot'].search(lot_domain)
            wizard.total_lots = len(all_lots)

            # Count lots with zero cost
            zero_cost_lots = all_lots.filtered(lambda l: l.avg_cost_per_weight <= 0)
            wizard.lots_with_zero_cost = len(zero_cost_lots)

            # Build final domain based on zero cost options
            if wizard.only_zero_cost_lots:
                lot_domain.append(('avg_cost_per_weight', '<=', 0))
            elif not wizard.include_zero_cost_lots:
                lot_domain.append(('avg_cost_per_weight', '>', 0))

            # Get lots to update
            lots_to_update = self.env['stock.lot'].search(lot_domain)
            wizard.lots_to_update = len(lots_to_update)

            # Count lots with valuation layers
            lots_with_valuation = 0
            for lot in lots_to_update:
                svl_domain = [
                    ('lot_id', '=', lot.id),
                    ('create_date', '>=', wizard.date_from),
                    ('create_date', '<=', wizard.date_to),
                ]
                if self.env['stock.valuation.layer'].search_count(svl_domain) > 0:
                    lots_with_valuation += 1

            wizard.lots_with_valuation = lots_with_valuation

    def _find_original_import_rate(self, lot):
        """
        Try to find the original import rate from stock move lines and valuation layers
        Uses a deterministic algorithm to find the most accurate value
        """
        _logger.info(f"Finding original import rate for lot {lot.name} (ID: {lot.id})")

        # Step 1: Find all receipt operations for this lot
        receipt_picking_types = self.env['stock.picking.type'].search([
            ('code', '=', 'incoming')
        ])

        domain = [
            ('lot_id', '=', lot.id),
        ]

        if self.receipt_pickings_only:
            receipt_pickings = self.env['stock.picking'].search([
                ('picking_type_id', 'in', receipt_picking_types.ids)
            ])
            domain.append(('picking_id', 'in', receipt_pickings.ids))

        # Look for stock move lines with this lot
        move_lines = self.env['stock.move.line'].search(domain, order='date')

        if not move_lines:
            _logger.info(f"No move lines found for lot {lot.name}")
            return self._find_value_from_all_valuation_layers(lot)

        # Step 2: Check if any receipt move line has a standard_price > 0
        receipt_move_lines = []
        for move_line in move_lines:
            if move_line.picking_id and move_line.picking_id.picking_type_id.code == 'incoming':
                receipt_move_lines.append(move_line)
                if move_line.standard_price > 0:
                    _logger.info(f"Found receipt move line with standard_price {move_line.standard_price} for lot {lot.name}")
                    return move_line.standard_price

        if not receipt_move_lines:
            _logger.info(f"No receipt move lines found for lot {lot.name}")
            return self._find_value_from_all_valuation_layers(lot)

        # Step 3: If no standard_price found, look at valuation layers for the receipt moves
        for move_line in receipt_move_lines:
            if move_line.move_id:
                # Find the valuation layer for this stock move
                valuation_layers = self.env['stock.valuation.layer'].search([
                    ('stock_move_id', '=', move_line.move_id.id)
                ], order='create_date')

                if valuation_layers:
                    # Step 3.1: Check for manual modifications
                    manual_mod_layers = valuation_layers.filtered(
                        lambda l: l.description and 'lot value manually modified' in l.description.lower()
                    )

                    if manual_mod_layers:
                        # Get the most recent manual modification
                        latest_manual_mod = manual_mod_layers[-1]

                        # Extract the new value from the description
                        if latest_manual_mod.description and 'to ' in latest_manual_mod.description:
                            try:
                                to_value_str = latest_manual_mod.description.split('to ')[-1].rstrip(')')
                                to_value = float(to_value_str)
                                _logger.info(f"Found manual modification with value {to_value} for move {move_line.move_id.id}")
                                return self._convert_to_per_weight_cost(to_value, lot.product_id.weight)
                            except (ValueError, IndexError):
                                _logger.warning(f"Could not extract value from description: {latest_manual_mod.description}")

                    # Step 3.2: Check for landed costs
                    original_layers = valuation_layers.filtered(
                        lambda l: l.unit_cost > 0 and not (l.description and 'lot value manually modified' in l.description.lower())
                    )

                    if original_layers:
                        original_layer = original_layers[0]

                        # Check if there are additional landed cost layers
                        landed_cost_layers = valuation_layers.filtered(
                            lambda l: l.id != original_layer.id and ('LC/' in (l.description or ''))
                        )

                        if landed_cost_layers and original_layer.quantity > 0:
                            # Calculate the total value including landed costs
                            total_value = sum(layer.value for layer in valuation_layers if layer.value)
                            total_qty = original_layer.quantity

                            # Calculate the adjusted unit cost
                            adjusted_unit_cost = total_value / total_qty
                            _logger.info(f"Calculated adjusted unit cost with landed costs: {adjusted_unit_cost} for move {move_line.move_id.id}")
                            return self._convert_to_per_weight_cost(adjusted_unit_cost, lot.product_id.weight)

                        # If no landed costs, use the original unit cost
                        _logger.info(f"Found original valuation layer with unit_cost {original_layer.unit_cost} for move {move_line.move_id.id}")
                        return self._convert_to_per_weight_cost(original_layer.unit_cost, lot.product_id.weight)

        # Step 4: If still no value, look at all valuation layers for this lot
        return self._find_value_from_all_valuation_layers(lot)

    def _find_value_from_all_valuation_layers(self, lot):
        """
        Find the best value from all valuation layers for this lot
        """
        _logger.info(f"Looking at all valuation layers for lot {lot.name}")

        # Get all valuation layers for this lot within the specified date range
        domain = [
            ('lot_id', '=', lot.id),
        ]

        # Apply date range if specified
        if self.date_from:
            domain.append(('create_date', '>=', self.date_from))
        if self.date_to:
            domain.append(('create_date', '<=', self.date_to))

        all_valuation_layers = self.env['stock.valuation.layer'].search(domain, order='create_date')

        if not all_valuation_layers:
            _logger.info(f"No valuation layers found for lot {lot.name}")
            # If no valuation layers found, check if the lot already has a non-zero avg_cost_per_weight
            if lot.avg_cost_per_weight > 0:
                _logger.info(f"Lot already has a non-zero avg_cost_per_weight: {lot.avg_cost_per_weight}")
                return lot.avg_cost_per_weight
            return 0

        # Step 1: Check for manual modifications (unless excluded)
        # This is critical - if there are manual modifications, they should take precedence
        manual_modification_layers = []
        if not self.exclude_manual_modifications:
            for layer in all_valuation_layers:
                if layer.description and 'lot value manually modified' in layer.description.lower():
                    manual_modification_layers.append(layer)

        if manual_modification_layers:
            # Get the most recent manual modification
            latest_manual_mod = manual_modification_layers[-1]
            _logger.info(f"Found manual modification layer: {latest_manual_mod.description}")

            # Extract the new value from the description
            # Format is typically "Lot value manually modified (from X to Y)"
            if latest_manual_mod.description and 'to ' in latest_manual_mod.description:
                try:
                    # Extract the value after "to "
                    to_value_str = latest_manual_mod.description.split('to ')[-1].rstrip(')')
                    to_value = float(to_value_str)
                    _logger.info(f"Extracted manual modification to value: {to_value}")
                    return self._convert_to_per_weight_cost(to_value, lot.product_id.weight)
                except (ValueError, IndexError):
                    _logger.warning(f"Could not extract value from description: {latest_manual_mod.description}")

            # If we couldn't extract the value from the description, use the lot's current value
            if lot.avg_cost_per_weight > 0:
                _logger.info(f"Using lot's current avg_cost_per_weight after manual modification: {lot.avg_cost_per_weight}")
                return lot.avg_cost_per_weight

        # Step 2: Look for receipt valuation layers with landed costs
        receipt_layers = all_valuation_layers.filtered(
            lambda l: l.stock_move_id and l.stock_move_id.picking_id and
                     l.stock_move_id.picking_id.picking_type_id.code == 'incoming'
        )

        if receipt_layers:
            # Group valuation layers by stock_move_id
            move_to_layers = {}
            for layer in receipt_layers:
                if layer.stock_move_id.id not in move_to_layers:
                    move_to_layers[layer.stock_move_id.id] = []
                move_to_layers[layer.stock_move_id.id].append(layer)

            # Process each move's layers
            for move_id, layers in move_to_layers.items():
                # Find the original receipt layer
                original_layers = [l for l in layers if l.unit_cost > 0 and
                                  not (l.description and 'lot value manually modified' in l.description.lower())]

                if original_layers:
                    original_layer = original_layers[0]

                    # Check if there are landed cost layers
                    landed_cost_layers = [l for l in layers if l.id != original_layer.id and
                                         ('LC/' in (l.description or ''))]

                    if landed_cost_layers and original_layer.quantity > 0:
                        # Calculate the total value including landed costs
                        total_value = sum(layer.value for layer in layers if layer.value)
                        total_qty = original_layer.quantity

                        # Calculate the adjusted unit cost
                        adjusted_unit_cost = total_value / total_qty
                        _logger.info(f"Calculated adjusted unit cost with landed costs: {adjusted_unit_cost}")
                        return self._convert_to_per_weight_cost(adjusted_unit_cost, lot.product_id.weight)

                    # If no landed costs, use the original unit cost
                    if original_layer.unit_cost > 0:
                        _logger.info(f"Found receipt valuation layer with unit_cost {original_layer.unit_cost}")
                        return self._convert_to_per_weight_cost(original_layer.unit_cost, lot.product_id.weight)

        # Step 3: Look for layers that indicate original valuation (e.g., "Initial IN")
        original_indicators = ['initial in', 'product receipt', 'reception']
        for layer in all_valuation_layers:
            if layer.description and any(indicator in layer.description.lower() for indicator in original_indicators):
                if layer.unit_cost > 0:
                    _logger.info(f"Found original valuation layer with description '{layer.description}' and unit_cost {layer.unit_cost}")
                    return self._convert_to_per_weight_cost(layer.unit_cost, lot.product_id.weight)

        # Step 4: Look for any non-zero valuation layer
        # First, check for incoming receipt valuation layers again (without landed costs)
        for layer in all_valuation_layers:
            # Check if this is from an incoming receipt
            if layer.stock_move_id and layer.stock_move_id.picking_id and layer.stock_move_id.picking_id.picking_type_id.code == 'incoming':
                # If the unit_cost is non-zero, use it
                if layer.unit_cost > 0:
                    _logger.info(f"Found receipt valuation layer with unit_cost {layer.unit_cost}")
                    return self._convert_to_per_weight_cost(layer.unit_cost, lot.product_id.weight)
                # Even if the value is zero, check if the lot has a non-zero avg_cost_per_weight
                elif lot.avg_cost_per_weight > 0:
                    _logger.info(f"Found receipt valuation layer with zero value, but lot has avg_cost_per_weight: {lot.avg_cost_per_weight}")
                    return lot.avg_cost_per_weight

        # Then, check for any non-zero valuation layer
        non_zero_layers = all_valuation_layers.filtered(
            lambda l: l.unit_cost > 0 and not (l.description and 'lot value manually modified' in l.description.lower()
                                              and float_is_zero(l.value, precision_digits=2))
        )

        if non_zero_layers:
            layer = non_zero_layers[0]
            _logger.info(f"Found non-zero valuation layer with unit_cost {layer.unit_cost}")
            return self._convert_to_per_weight_cost(layer.unit_cost, lot.product_id.weight)

        # Step 5: If the lot already has a non-zero avg_cost_per_weight, use that
        if lot.avg_cost_per_weight > 0:
            _logger.info(f"Using existing lot avg_cost_per_weight: {lot.avg_cost_per_weight}")
            return lot.avg_cost_per_weight

        # Step 6: If all else fails, return 0
        _logger.info(f"No suitable valuation found for lot {lot.name}")
        return 0

    def _convert_to_per_weight_cost(self, unit_cost, weight):
        """
        Convert a unit cost to a per-weight cost
        """
        if weight > 0:
            per_weight_cost = unit_cost / weight
            _logger.info(f"Converted unit cost {unit_cost} to per-weight cost {per_weight_cost} (weight: {weight})")
            return per_weight_cost

        _logger.info(f"Using unit cost {unit_cost} directly (product has no weight)")
        return unit_cost

    def _find_pre_zero_reset_value(self, valuation_layers):
        """
        Find the last valuation layer before any that set the value to 0
        """
        if not valuation_layers:
            return 0

        # Sort by create_date
        sorted_layers = valuation_layers.sorted(key=lambda l: l.create_date)

        # Find the last layer before any zero-value manual modification
        last_good_layer = None
        for layer in sorted_layers:
            # Check if this is a manual modification to zero
            is_zero_reset = (
                layer.description and
                'Lot value manually modified' in layer.description and
                float_is_zero(layer.value, precision_digits=2)
            )

            if is_zero_reset:
                # If we found a reset to zero and have a previous good layer, return that
                if last_good_layer:
                    return last_good_layer.unit_cost
            else:
                # Otherwise, update our last good layer
                last_good_layer = layer

        # If we didn't find a reset to zero, return the last layer's value
        return sorted_layers[-1].unit_cost if sorted_layers else 0

    def _find_first_non_zero_valuation(self, valuation_layers):
        """
        Find the first valuation layer with a non-zero value
        """
        if not valuation_layers:
            return 0

        # Sort by create_date
        sorted_layers = valuation_layers.sorted(key=lambda l: l.create_date)

        # Find the first layer with non-zero value
        for layer in sorted_layers:
            if not float_is_zero(layer.unit_cost, precision_digits=2):
                return layer.unit_cost

        return 0

    def _smart_restore_value(self, lot, valuation_layers):
        """
        Intelligently determine the best method to restore the lot price
        based on the valuation history
        """
        # First try to find the original import rate
        import_rate = self._find_original_import_rate(lot)
        if import_rate > 0:
            return import_rate, "Smart Restore: Using original import rate"

        if not valuation_layers:
            return 0, "Smart Restore: No valuation layers found"

        # Check if there are any manual modifications to zero
        has_zero_reset = False
        for layer in valuation_layers:
            if (layer.description and
                'Lot value manually modified' in layer.description and
                float_is_zero(layer.value, precision_digits=2)):
                has_zero_reset = True
                break

        if has_zero_reset:
            # If there was a reset to zero, use the pre-zero-reset method
            value = self._find_pre_zero_reset_value(valuation_layers)
            if value > 0:
                return value, "Smart Restore: Using last value before reset to zero"

        # If we have multiple layers, check if there's a consistent pattern
        if len(valuation_layers) > 1:
            # If the first layer has a non-zero value, use that
            first_non_zero = self._find_first_non_zero_valuation(valuation_layers)
            if first_non_zero > 0:
                return first_non_zero, "Smart Restore: Using first non-zero valuation"

        # If all else fails, use the last valuation layer
        return valuation_layers[-1].unit_cost, "Smart Restore: Using last valuation layer"

    def action_restore_lot_prices(self):
        """
        Restore lot prices based on stock valuation layers
        """
        self.ensure_one()

        # Initialize log
        log_lines = []
        log_lines.append(f"<h3>Lot Price Restoration Report</h3>")
        log_lines.append(f"<p>Started at: {datetime.now()}</p>")
        log_lines.append(f"<p>Date range: {self.date_from} to {self.date_to}</p>")
        log_lines.append(f"<p>Preview only: {'Yes' if self.preview_only else 'No'}</p>")

        # Find receipt move lines with zero or null standard_price
        receipt_picking_types = self.env['stock.picking.type'].search([
            ('code', '=', 'incoming')
        ])

        move_line_domain = [
            ('picking_id.picking_type_id', 'in', receipt_picking_types.ids),
        ]

        # Add standard_price filter
        if self.only_zero_cost_lots:
            move_line_domain.append('|')
            move_line_domain.append(('standard_price', '=', 0))
            move_line_domain.append(('standard_price', '=', False))

        # Add date range if specified
        if self.date_from:
            move_line_domain.append(('date', '>=', self.date_from))
        if self.date_to:
            move_line_domain.append(('date', '<=', self.date_to))

        # Add specific lots or products if specified
        if self.lot_ids:
            move_line_domain.append(('lot_id', 'in', self.lot_ids.ids))
        if self.product_ids:
            move_line_domain.append(('product_id', 'in', self.product_ids.ids))

        # Get move lines to process
        move_lines = self.env['stock.move.line'].search(move_line_domain)
        log_lines.append(f"<p>Found {len(move_lines)} receipt move lines to process</p>")

        # Start table for detailed results
        log_lines.append("<table class='table table-bordered'>")
        log_lines.append("<thead><tr><th>Move Line</th><th>Lot</th><th>Product</th><th>Qty</th><th>Weight (kg)</th><th>Current Price</th><th>New Price</th><th>Total Value</th><th>Status</th></tr></thead>")
        log_lines.append("<tbody>")

        # Process each move line
        updated_move_lines = 0
        skipped_move_lines = 0

        for move_line in move_lines:
            # Skip if no lot
            if not move_line.lot_id:
                log_lines.append(f"<tr><td>{move_line.id}</td><td>-</td><td>{move_line.product_id.name}</td><td>{move_line.quantity}</td><td>{move_line.product_id.weight}</td><td>-</td><td>-</td><td>-</td><td>No lot assigned</td></tr>")
                skipped_move_lines += 1
                continue

            lot = move_line.lot_id
            stock_move = move_line.move_id

            # Initialize price and status
            new_price = 0
            status = ""

            # Find the valuation layer specifically for this move
            if stock_move:
                valuation_layers = self.env['stock.valuation.layer'].search([
                    ('stock_move_id', '=', stock_move.id)
                ], order='create_date')

                if valuation_layers:
                    # Step 1: Check for manual modifications related to this move (unless excluded)
                    manual_mod_layers = self.env['stock.valuation.layer']
                    if not self.exclude_manual_modifications:
                        manual_mod_layers = valuation_layers.filtered(
                            lambda l: l.description and 'lot value manually modified' in l.description.lower()
                        )

                    # If no manual modifications found for this move, check for manual modifications for this lot
                    if not manual_mod_layers and not self.exclude_manual_modifications:
                        # Find all valuation layers for this lot
                        lot_valuation_layers = self.env['stock.valuation.layer'].search([
                            ('lot_id', '=', lot.id)
                        ], order='create_date')

                        # Check for manual modifications
                        lot_manual_mod_layers = lot_valuation_layers.filtered(
                            lambda l: l.description and 'lot value manually modified' in l.description.lower()
                        )

                        # If we found manual modifications for the lot, check if any are close in time to this move
                        if lot_manual_mod_layers:
                            move_date = fields.Datetime.from_string(move_line.date)
                            _logger.info(f"Move date: {move_date}")

                            # Look for manual modifications that happened within 1 minute after the move
                            very_close_mods = [l for l in lot_manual_mod_layers if
                                             fields.Datetime.from_string(l.create_date) > move_date and
                                             (fields.Datetime.from_string(l.create_date) - move_date).total_seconds() < 60]

                            if very_close_mods:
                                _logger.info(f"Found {len(very_close_mods)} manual modifications for lot within 1 minute after the move")
                                manual_mod_layers = self.env['stock.valuation.layer'].browse([l.id for l in very_close_mods])

                    if manual_mod_layers:
                        # Get the most recent manual modification
                        latest_manual_mod = manual_mod_layers[-1]
                        _logger.info(f"Found manual modification layer for move {stock_move.id}: {latest_manual_mod.description}")

                        # Extract the new value from the description
                        if latest_manual_mod.description and 'to ' in latest_manual_mod.description:
                            try:
                                # Extract the value after "to "
                                to_value_str = latest_manual_mod.description.split('to ')[-1].rstrip(')')
                                to_value = float(to_value_str)
                                _logger.info(f"Extracted manual modification to value: {to_value}")
                                new_price = to_value
                                status = f"Using manually modified value: {to_value}"
                            except (ValueError, IndexError):
                                _logger.warning(f"Could not extract value from description: {latest_manual_mod.description}")

                    # Step 2: If no manual modifications, check if we need to account for landed costs
                    if not new_price:
                        # First, find the original receipt valuation layer
                        original_layers = valuation_layers.filtered(
                            lambda l: l.unit_cost > 0 and not (l.description and 'lot value manually modified' in l.description.lower())
                        )

                        # Log for debugging
                        _logger.info(f"Found {len(original_layers)} original layers with unit_cost > 0")

                        if original_layers:
                            original_layer = original_layers[0]

                            # Check if there are additional landed cost layers
                            landed_cost_layers = valuation_layers.filtered(
                                lambda l: l.id != original_layer.id and ('LC/' in (l.description or ''))
                            )

                            if landed_cost_layers and original_layer.quantity > 0:
                                # Calculate the total value including landed costs
                                total_value = sum(layer.value for layer in valuation_layers if layer.value)
                                total_qty = original_layer.quantity

                                # Calculate the adjusted unit cost
                                adjusted_unit_cost = total_value / total_qty
                                new_price = adjusted_unit_cost
                                status = f"Using unit cost with landed costs: {new_price}"
                                _logger.info(f"Calculated adjusted unit cost with landed costs: {new_price}")
                            else:
                                # If no landed costs or calculation failed, use the original unit cost
                                new_price = original_layer.unit_cost
                                status = f"Using original valuation layer: {new_price}"
                                _logger.info(f"Using original valuation layer unit cost: {new_price}")

            # Step 3: If no price found from move-specific valuation layers, try to determine from lot
            if not new_price:
                # Find all valuation layers for this lot
                all_lot_layers = self.env['stock.valuation.layer'].search([
                    ('lot_id', '=', lot.id)
                ], order='create_date')

                _logger.info(f"Found {len(all_lot_layers)} valuation layers for lot {lot.name}")
                for layer in all_lot_layers:
                    _logger.info(f"Layer ID: {layer.id}, Description: {layer.description}, Value: {layer.value}, Unit Cost: {layer.unit_cost}, Create Date: {layer.create_date}")

                # Check for manual modifications
                manual_mod_layers = all_lot_layers.filtered(
                    lambda l: l.description and 'lot value manually modified' in l.description.lower()
                )

                _logger.info(f"Found {len(manual_mod_layers)} manual modification layers for lot {lot.name}")
                for layer in manual_mod_layers:
                    _logger.info(f"Manual Mod Layer ID: {layer.id}, Description: {layer.description}, Create Date: {layer.create_date}")

                if manual_mod_layers:
                    # Use timestamp proximity to find the most relevant manual modification
                    # Get the manual modification closest to the move line's date
                    move_date = fields.Datetime.from_string(move_line.date)
                    _logger.info(f"Move date: {move_date}")

                    # First try to find manual modifications that happened right after the move (within 1 minute)
                    very_close_mods = [l for l in manual_mod_layers if
                                     (fields.Datetime.from_string(l.create_date) - move_date).total_seconds() > 0 and
                                     (fields.Datetime.from_string(l.create_date) - move_date).total_seconds() < 60]

                    if very_close_mods:
                        _logger.info(f"Found {len(very_close_mods)} manual modifications within 1 minute after the move")
                        closest_manual_mod = very_close_mods[0]  # Take the first one if multiple
                    else:
                        # If no very close modifications, use the closest one by absolute time difference
                        closest_manual_mod = min(manual_mod_layers,
                                               key=lambda l: abs((fields.Datetime.from_string(l.create_date) - move_date).total_seconds()))
                        time_diff = abs((fields.Datetime.from_string(closest_manual_mod.create_date) - move_date).total_seconds())
                        _logger.info(f"Using closest manual modification with time difference of {time_diff} seconds")

                    _logger.info(f"Selected manual modification: {closest_manual_mod.description}")

                    # Extract the new value from the description
                    if closest_manual_mod.description and 'to ' in closest_manual_mod.description:
                        try:
                            to_value_str = closest_manual_mod.description.split('to ')[-1].rstrip(')')
                            to_value = float(to_value_str)
                            new_price = to_value
                            status = f"Using manually modified value (by timestamp proximity): {to_value}"
                            _logger.info(f"Extracted manual modification value: {to_value}")
                        except (ValueError, IndexError):
                            _logger.warning(f"Could not extract value from description: {closest_manual_mod.description}")

                # If still no price, use our deterministic algorithm as a fallback
                if not new_price:
                    lot_price = self._find_original_import_rate(lot)
                    if lot_price > 0:
                        new_price = lot_price * lot.product_id.weight  # Convert back to unit price
                        status = f"Using calculated lot price: {lot_price} per kg"
                        _logger.info(f"Using calculated lot price: {lot_price} per kg")

            # Skip if no price found
            if not new_price:
                status = "No suitable price found"
                qty = move_line.quantity
                weight = lot.product_id.weight
                current_price = move_line.standard_price or 0
                total_value = '-'
                log_lines.append(f"<tr><td>{move_line.id}</td><td>{lot.name}</td><td>{lot.product_id.name}</td><td>{qty}</td><td>{weight}</td><td>{current_price}</td><td>-</td><td>{total_value}</td><td>{status}</td></tr>")
                skipped_move_lines += 1
                continue

            # Update the move line if not in preview mode
            current_price = move_line.standard_price or 0

            if not self.preview_only:
                move_line.write({
                    'standard_price': new_price,
                })
                status += " - Updated"
            else:
                status += " - Preview only"

            # Calculate total value
            qty = move_line.quantity
            weight = lot.product_id.weight
            total_value = new_price * qty

            # Format with 2 decimal places for better readability
            current_price_fmt = f"{current_price:.2f}" if isinstance(current_price, (int, float)) else current_price
            new_price_fmt = f"{new_price:.2f}" if isinstance(new_price, (int, float)) else new_price
            total_value_fmt = f"{total_value:.2f}" if isinstance(total_value, (int, float)) else total_value

            log_lines.append(f"<tr><td>{move_line.id}</td><td>{lot.name}</td><td>{lot.product_id.name}</td><td>{qty}</td><td>{weight}</td><td>{current_price_fmt}</td><td>{new_price_fmt}</td><td>{total_value_fmt}</td><td>{status}</td></tr>")
            updated_move_lines += 1

            # We do NOT update the lot's avg_cost_per_weight as there could have been further operations
            # on the lot and we would be setting the wrong value, making the system inconsistent

        # Close table
        log_lines.append("</tbody></table>")

        # Update log with summary
        log_lines.append(f"<h4>Summary:</h4>")
        log_lines.append(f"<ul>")
        log_lines.append(f"<li>Total move lines processed: {len(move_lines)}</li>")
        log_lines.append(f"<li>Move lines updated: {updated_move_lines}</li>")
        log_lines.append(f"<li>Move lines skipped: {skipped_move_lines}</li>")
        log_lines.append(f"</ul>")
        log_lines.append(f"<p>Completed at {datetime.now()}</p>")

        # Update the log field
        self.log_details = "\n".join(log_lines)

        # Create scheduled action if requested
        if self.auto_schedule and not self.preview_only:
            self._create_scheduled_action()
            log_lines.append(f"<p>Created scheduled action to run automatically ({self.schedule_interval})</p>")

        return {
            'type': 'ir.actions.act_window',
            'res_model': 'restore.lot.price.wizard',
            'view_mode': 'form',
            'res_id': self.id,
            'target': 'new',
            'context': self.env.context,
        }

    def _create_scheduled_action(self):
        """
        Create a scheduled action to run the lot price restoration automatically
        """
        # Check if a scheduled action already exists
        existing_action = self.env['ir.cron'].search([
            ('name', 'like', 'Restore Lot Prices'),
            ('model_id.model', '=', 'restore.lot.price.wizard')
        ])

        if existing_action:
            # Update the existing action with minimal fields
            update_vals = {
                'active': True
            }

            # Try to set other fields that might be valid in Odoo 17.0
            try:
                update_vals.update({
                    'interval_type': self.schedule_interval,
                    'code': self._get_scheduled_action_code(),
                })
            except Exception as e:
                _logger.warning(f"Could not set all cron job fields for update: {e}")

            existing_action.write(update_vals)
            return existing_action

        # Create a minimal cron job with only essential fields for Odoo 17.0
        vals = {
            'name': 'Restore Lot Prices - Automatic',
            'model_id': self.env['ir.model'].search([('model', '=', 'restore.lot.price.wizard')], limit=1).id,
            'active': True,
        }

        # Try to set other fields that might be valid in Odoo 17.0
        try:
            vals.update({
                'state': 'code',
                'code': self._get_scheduled_action_code(),
                'user_id': self.env.ref('base.user_root').id,
                'interval_number': 1,
                'interval_type': self.schedule_interval,
                'nextcall': (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S'),
            })
        except Exception as e:
            _logger.warning(f"Could not set all cron job fields: {e}")

        return self.env['ir.cron'].create(vals)

    def _get_scheduled_action_code(self):
        """
        Generate the Python code for the scheduled action
        """
        code = f"""
# Create a new wizard
wizard = env['restore.lot.price.wizard'].create({{
    'date_from': (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'),
    'date_to': datetime.now().strftime('%Y-%m-%d'),
    'only_zero_cost_lots': {str(self.only_zero_cost_lots).lower()},
    'include_zero_cost_lots': {str(self.include_zero_cost_lots).lower()},
    'exclude_manual_modifications': {str(self.exclude_manual_modifications).lower()},
    'receipt_pickings_only': True,  # Always focus on receipt operations for scheduled runs
    'preview_only': False,
}})

# Run the restoration
wizard.action_restore_lot_prices()

# Log the result
log_message = f"Automatic lot price restoration completed at {{datetime.now()}}."
_logger.info(log_message)
"""
        return code

    @api.model
    def run_scheduled_restoration(self):
        """
        Method to be called by scheduled action
        """
        _logger.info("Starting scheduled lot price restoration")

        # Create a wizard with optimal settings for automatic restoration
        wizard = self.create({
            'date_from': (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'),
            'date_to': datetime.now().strftime('%Y-%m-%d'),
            'only_zero_cost_lots': True,  # Focus on move lines with zero standard_price
            'include_zero_cost_lots': False,
            'exclude_manual_modifications': False,  # Include all valuation layers for analysis
            'receipt_pickings_only': True,  # Focus on receipt operations
            'preview_only': False,  # Actually update the move lines
        })

        result = wizard.action_restore_lot_prices()

        # Log detailed results
        _logger.info(f"Scheduled lot price restoration completed.")

        return result
