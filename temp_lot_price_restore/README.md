# Temporary Lot Price Restore

This module provides a tool to restore lot prices from stock valuation data. It was created to address an issue where lot prices were being reset to 0 after confirmation, despite being entered correctly using the Import Rate functionality.

## Problem

The Import Rate functionality allows users to enter lot prices during receipt operations. However, after confirmation, these prices are sometimes reset to 0, causing issues with product rate calculations.

## Solution

This module analyzes stock valuation layers to identify the correct lot prices and updates the lot records accordingly. It provides a wizard interface with various options for restoring lot prices:

- **First Valuation Layer**: Use the cost from the first valuation layer
- **Last Valuation Layer**: Use the cost from the most recent valuation layer
- **Average of Valuation Layers**: Use the average cost from all valuation layers
- **Maximum Value**: Use the highest cost from all valuation layers
- **Original Import Rate**: Try to find the original import rate from stock move lines

## Usage

1. Go to Inventory > Operations > Restore Lot Prices
2. Set the date range for valuation layers to consider
3. Choose the update method
4. Optionally filter by specific products or lots
5. Use the "Preview Only" option to see what changes would be made without actually updating the database
6. Click "Restore Lot Prices" to run the process

## Technical Details

The module works by:
1. Analyzing stock valuation layers associated with lots
2. Calculating the appropriate cost based on the selected method
3. Updating the lot's `avg_cost_per_weight` field with the calculated value

This ensures that lot prices are correctly maintained and can be used for accurate product rate calculations.
