{
    'name': 'Admin Date Edit',
    'version': '1.0',
    'category': 'Administration',
    'summary': 'Allow administrators to change dates on confirmed documents with full data consistency',
    'description': """
Admin Date Edit
==============
This module allows administrators to change dates on confirmed documents while maintaining complete data consistency across the Odoo system:

Supported Documents
------------------
- Stock Picking (scheduled_date, date_done)
- Purchase Orders (date_order, date_planned, date_approve)
- Sale Orders (date_order, commitment_date)
- Manufacturing Orders (date_planned_start, date_planned_finished, date_deadline)

Comprehensive Data Consistency
-----------------------------
Stock Picking (Transfers):
* Updates stock moves, move lines, and valuation layers
* Updates stock quants with correct in/out dates
* Updates accounting entries linked to the picking
* Updates landed costs if they exist
* Updates inventory adjustments if linked
* Updates lot/serial numbers for incoming pickings
* Adds detailed audit logs for all changes

Purchase Order:
* Updates invoice dates and accounting entries
* Updates stock picking scheduled dates
* Updates payment term calculations
* Updates stock moves linked to purchase order lines
* Updates purchase order line dates
* Handles both direct and indirect relationships

Sale Order:
* Updates invoice dates and accounting entries
* Updates stock picking scheduled dates
* Updates manufacturing orders linked through procurement groups
* Updates payment term calculations
* Updates stock moves linked to sale order lines
* Updates sale order line dates
* Handles both direct and indirect relationships

Manufacturing Order:
* Updates stock move dates for raw materials and finished products
* Updates stock valuation layers with correct dates
* Updates stock quants with correct in/out dates
* Updates work orders with correct planned dates
* Updates accounting entries related to production
* Updates quality checks if they exist
* Updates maintenance requests if they exist
* Updates lot/serial numbers created during production
* Adds detailed audit logs for all changes

Key Features
-----------
* Comprehensive Data Consistency: All related records are updated to maintain consistency
* Intelligent Date Propagation: Changes to dates are intelligently propagated to related documents
* Audit Trail: Detailed logging of all changes for audit purposes
* Error Prevention: Checks for posted accounting entries that cannot be modified
* Security: Only administrators with specific permissions can change dates

Technical Implementation
----------------------
* Modular Design: Each document type has its own model extension
* Odoo 18 Compatibility: Uses the correct syntax for disabled attribute in form views
* Robust Error Handling: Checks for existence of optional modules before updating their records
    """,
    'author': 'Arihant AI',
    'website': 'https://www.arihantai.com',
    'depends': [
        'base',
        'stock',
        'purchase',
        'sale_management',
        'mrp',
    ],
    'data': [
        'security/security.xml',
        'security/ir.model.access.csv',
        'views/stock_picking_views.xml',
        'views/purchase_order_views.xml',
        'views/sale_order_views.xml',
        'views/mrp_production_views.xml',
    ],
    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
}
