from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging
from datetime import datetime

_logger = logging.getLogger(__name__)

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    can_edit_dates = fields.Boolean(
        compute='_compute_can_edit_dates',
        string='Can Edit Dates',
        help='Technical field to determine if the user can edit dates on confirmed sale orders'
    )

    @api.depends('state')
    def _compute_can_edit_dates(self):
        """Determine if the user can edit dates on this sale order."""
        is_admin_date_editor = self.env.user.has_group('ai_admin_date_edit.group_admin_date_edit')
        for order in self:
            # Allow editing dates if the user is in the admin date edit group and the order is confirmed
            order.can_edit_dates = is_admin_date_editor and order.state in ['sale', 'done']

    def action_edit_dates(self):
        """Open a wizard to edit dates on the sale order."""
        self.ensure_one()
        if not self.can_edit_dates:
            raise UserError(_("You don't have permission to edit dates on this sale order."))

        return {
            'name': _('Edit Dates'),
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'res_model': 'sale.order',
            'res_id': self.id,
            'target': 'new',
            'context': {
                'edit_dates': True,
            },
        }

    def write(self, vals):
        """Override write to allow changing dates on confirmed sale orders."""
        # Check if we're editing dates and if the user has permission
        is_admin_date_editor = self.env.user.has_group('ai_admin_date_edit.group_admin_date_edit')
        is_editing_dates = any(field in vals for field in ['date_order', 'commitment_date'])

        # Store original values for later processing
        orders_with_date_changes = {}
        if is_editing_dates:
            for order in self:
                if order.state in ['sale', 'done']:
                    changes = {}
                    if 'date_order' in vals and order.date_order != vals['date_order']:
                        changes['date_order'] = {'old': order.date_order, 'new': vals['date_order']}
                    if 'commitment_date' in vals and order.commitment_date != vals['commitment_date']:
                        changes['commitment_date'] = {'old': order.commitment_date, 'new': vals['commitment_date']}

                    if changes:
                        orders_with_date_changes[order.id] = changes

        # If editing dates on confirmed orders, ensure the user has permission
        if is_editing_dates and any(order.state in ['sale', 'done'] for order in self):
            if not is_admin_date_editor:
                raise UserError(_("You don't have permission to edit dates on confirmed sale orders."))

            # Log the date changes for audit purposes
            for order in self:
                if order.state in ['sale', 'done']:
                    changes = []
                    if 'date_order' in vals and order.date_order != vals['date_order']:
                        changes.append(f"date_order: {order.date_order} -> {vals['date_order']}")
                    if 'commitment_date' in vals and order.commitment_date != vals['commitment_date']:
                        changes.append(f"commitment_date: {order.commitment_date} -> {vals['commitment_date']}")

                    if changes:
                        _logger.info(f"Admin {self.env.user.name} changed dates on sale order {order.name}: {', '.join(changes)}")

                        # Update related pickings if commitment_date is changed
                        if 'commitment_date' in vals and order.picking_ids:
                            for picking in order.picking_ids.filtered(lambda p: p.state not in ['done', 'cancel']):
                                picking.scheduled_date = vals['commitment_date']
                                _logger.info(f"Updated scheduled_date on picking {picking.name} to {vals['commitment_date']}")

        # Proceed with the write operation
        result = super(SaleOrder, self).write(vals)

        # Update related records if dates were changed
        if orders_with_date_changes and is_admin_date_editor:
            self._update_related_records_after_date_change(orders_with_date_changes)

        return result

    def _update_related_records_after_date_change(self, orders_with_date_changes):
        """Update related records when dates are changed on a confirmed sale order."""
        for order_id, changes in orders_with_date_changes.items():
            order = self.browse(order_id)

            # Log the changes
            change_descriptions = []
            for field, values in changes.items():
                change_descriptions.append(f"{field}: {values['old']} -> {values['new']}")

            _logger.info(f"Updating related records for sale order {order.name} after date changes: {', '.join(change_descriptions)}")

            # 1. Update invoice dates if date_order was changed
            if 'date_order' in changes and order.invoice_ids:
                new_date = changes['date_order']['new']
                for invoice in order.invoice_ids:
                    if invoice.state not in ['posted', 'cancel']:
                        invoice.write({
                            'invoice_date': new_date.date() if hasattr(new_date, 'date') else new_date,
                            'date': new_date.date() if hasattr(new_date, 'date') else new_date,
                        })
                        _logger.info(f"Updated invoice_date and date on invoice {invoice.name} to {new_date}")

            # 2. Update accounting entries related to this sale order
            if 'date_order' in changes:
                new_date = changes['date_order']['new']
                # Find all account moves related to this sale order
                account_moves = self.env['account.move'].search([
                    '|',
                    ('invoice_origin', '=', order.name),
                    ('ref', '=', order.name)
                ])

                if account_moves:
                    for move in account_moves:
                        if move.state != 'posted':
                            move.write({'date': new_date})
                            _logger.info(f"Updated date on account move {move.name} to {new_date}")
                        else:
                            _logger.warning(f"Account move {move.name} is already posted and cannot be updated automatically")

            # 3. Update stock picking dates if commitment_date was changed
            if 'commitment_date' in changes and order.picking_ids:
                new_date = changes['commitment_date']['new']
                for picking in order.picking_ids:
                    if picking.state not in ['done', 'cancel']:
                        picking.write({'scheduled_date': new_date})
                        _logger.info(f"Updated scheduled_date on picking {picking.name} to {new_date}")

            # 4. Update manufacturing orders if they exist and are linked to this sale order
            if ('commitment_date' in changes or 'date_order' in changes):
                new_date = changes.get('commitment_date', {}).get('new') or changes.get('date_order', {}).get('new')

                # Try to find manufacturing orders linked to this sale order
                # First check if production_ids exists directly on the sale order
                if hasattr(order, 'production_ids') and order.production_ids:
                    productions = order.production_ids
                else:
                    # Otherwise, try to find MOs through procurement groups
                    procurement_groups = self.env['procurement.group'].search([('sale_id', '=', order.id)])
                    if procurement_groups:
                        productions = self.env['mrp.production'].search([('procurement_group_id', 'in', procurement_groups.ids)])
                    else:
                        productions = self.env['mrp.production'].browse()

                if productions:
                    for production in productions.filtered(lambda p: p.state not in ['done', 'cancel']):
                        production.write({
                            'date_planned_finished': new_date,
                            'date_deadline': new_date,
                        })
                        _logger.info(f"Updated dates on manufacturing order {production.name} to {new_date}")

            # 5. Update payment terms calculation if date_order was changed
            if 'date_order' in changes and order.payment_term_id:
                new_date = changes['date_order']['new']
                # Recalculate payment due dates
                payment_date = order.payment_term_id.compute(order.amount_total, new_date)[0][0]
                if payment_date:
                    _logger.info(f"Payment due date recalculated to {payment_date} based on new order date")

            # 6. Update stock moves directly linked to this sale order
            if 'commitment_date' in changes:
                new_date = changes['commitment_date']['new']
                # Find all stock moves related to this sale order
                stock_moves = self.env['stock.move'].search([
                    ('sale_line_id', 'in', order.order_line.ids),
                    ('state', 'not in', ['done', 'cancel'])
                ])

                if stock_moves:
                    stock_moves.write({'date': new_date})
                    _logger.info(f"Updated date on {len(stock_moves)} stock moves to {new_date}")

            # 7. Update sale order lines dates
            if 'commitment_date' in changes:
                new_date = changes['commitment_date']['new']
                for line in order.order_line:
                    if hasattr(line, 'commitment_date'):
                        line.write({'commitment_date': new_date})
                _logger.info(f"Updated commitment_date on sale order lines to {new_date}")

            # Add a note to the order for audit purposes
            order.message_post(
                body=_("Dates changed by %s. Related records have been updated accordingly: %s") %
                     (self.env.user.name, ', '.join(change_descriptions)),
                subtype_id=self.env.ref('mail.mt_note').id
            )

class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    def write(self, vals):
        """Override write to allow changing dates on confirmed sale order lines."""
        # Check if we're editing dates and if the user has permission
        is_admin_date_editor = self.env.user.has_group('ai_admin_date_edit.group_admin_date_edit')
        is_editing_dates = 'commitment_date' in vals

        # If editing dates on confirmed order lines, ensure the user has permission
        if is_editing_dates and any(line.state in ['sale', 'done'] for line in self):
            if not is_admin_date_editor:
                raise UserError(_("You don't have permission to edit dates on confirmed sale order lines."))

            # Log the date changes for audit purposes
            for line in self:
                if line.state in ['sale', 'done'] and line.commitment_date != vals['commitment_date']:
                    _logger.info(f"Admin {self.env.user.name} changed commitment_date on sale order line {line.id} "
                                f"(order {line.order_id.name}): {line.commitment_date} -> {vals['commitment_date']}")

                    # Update related move lines if commitment_date is changed
                    if line.move_ids:
                        for move in line.move_ids.filtered(lambda m: m.state not in ['done', 'cancel']):
                            move.date = vals['commitment_date']
                            _logger.info(f"Updated date on stock move {move.id} to {vals['commitment_date']}")

        # Proceed with the write operation
        return super(SaleOrderLine, self).write(vals)
