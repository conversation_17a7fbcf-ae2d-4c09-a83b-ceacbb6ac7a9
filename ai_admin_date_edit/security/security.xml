<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Create a security group for administrators who can edit dates -->
        <record id="group_admin_date_edit" model="res.groups">
            <field name="name">Admin Date Edit</field>
            <field name="category_id" ref="base.module_category_hidden"/>
            <field name="implied_ids" eval="[(4, ref('base.group_system'))]"/>
            <field name="users" eval="[(4, ref('base.user_admin'))]"/>
        </record>
    </data>
</odoo>
