<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- View for adding products to picking list view -->
    <record id="product_product_tree_view" model="ir.ui.view">
        <field name="name">
            product.product.view.list.inherit.products.to.transfer
        </field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_product_tree_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='default_code']" position="before">
                <button name="action_add_to_picking" type="object"
                        icon="fa-plus" title="Add to Picking" />
                <button name="action_view_details" type="object"
                        icon="fa-list" title="View Details"/>
            </xpath>
        </field>
    </record>
    <!-- View for adding products to picking kanban view -->
    <record id="product_kanban_view" model="ir.ui.view">
        <field name="name">
            product.product.view.kanban.inherit.products.to.transfer
        </field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_kanban_view"/>
        <field name="arch" type="xml">
            <xpath expr="//main" position="inside">
                <div style="display: inline-block; padding-top:10%;">
                    <a name="action_add_to_picking" type="object">
                        <i class="fa fa-plus"/>Add to Transfer
                    </a>
                    <a name="action_view_details" type="object">
                        <i class="fa fa-list"/>Update Quantity
                    </a>
                </div>
            </xpath>
        </field>
    </record>
    <!-- View for adding products to picking form view -->
    <record id="product_normal_form_view" model="ir.ui.view">
        <field name="name">
            product.product.view.form.inherit.products.to.transfer
        </field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_normal_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//header" position="inside">
                <button name="action_add_to_picking" type="object"
                        string="Add to Transfer" class="oe_highlight"
                        icon="fa-plus"/>
                <button name="action_view_details" type="object"
                        class="oe_highlight" icon="fa-list"
                        string="Update Quantity"/>
            </xpath>
        </field>
    </record>
</odoo>
