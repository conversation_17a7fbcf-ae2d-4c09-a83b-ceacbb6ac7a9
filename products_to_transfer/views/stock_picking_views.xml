<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- View for extending picking form and adding products -->
    <record id="view_picking_form" model="ir.ui.view">
        <field name="name">
            stock.picking.view.form.inherit.products.to.transfer
        </field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='action_picking_move_tree']"
                   position="after">
                <button name="action_view_products" type="object"
                        class="oe_stat_button" string="Add Products"
                        icon="fa-plus"
                        invisible="state != 'draft'"/>
            </xpath>
        </field>
    </record>
</odoo>
