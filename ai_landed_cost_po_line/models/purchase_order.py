from odoo import api, fields, models
import logging
import traceback

_logger = logging.getLogger(__name__)

class PurchaseOrderLine(models.Model):
    _inherit = 'purchase.order.line'

    landed_cost_ids = fields.Many2many(
        'stock.landed.cost',
        string='Landed Costs',
        copy=False,
        help='Landed costs applied to this purchase order line'
    )
    landed_cost_amount = fields.Monetary(
        string='Landed Cost Amount',
        help='User defined landed cost amount for this line'
    )
    
    def _create_or_update_picking_landed_cost(self, picking):
        """Create or update landed cost for the given picking"""
        _logger.info(f"DEBUG: Starting _create_or_update_picking_landed_cost for Purchase Order Line {self.id}")
        _logger.info(f"DEBUG: Picking {picking.id}, Purchase Line {self.id}")
        _logger.info(f"DEBUG: Landed Cost Amount: {self.landed_cost_amount}")
        
        # Log additional context
        _logger.info(f"DEBUG: Purchase Order: {self.order_id.id}")
        _logger.info(f"DEBUG: Product: {self.product_id.name}")
        
        if not self.landed_cost_amount:
            _logger.warning(f"No landed cost amount set for Purchase Order Line {self.id}. Skipping landed cost creation.")
            return
            
        LandedCost = self.env['stock.landed.cost']
        cost_lines_vals = []
        
        try:
            # Find Landed Costs Product
            product = self.env['product.template'].sudo().search([('name', '=', 'Landed Costs')], limit=1)
            _logger.info(f"DEBUG: Landed Costs Product found: {product}")
            
            if not product:
                _logger.error("No 'Landed Costs' product found. Cannot create landed cost.")
                return
            
            # Prepare Cost Lines
            cost_lines_vals.append({
                'product_id': product.product_variant_id.id,
                'price_unit': self.landed_cost_amount,
                'split_method': 'by_po_line_cost',
                'name': f'Additional Cost for {self.product_id.name}'
            })
            
            # Find General Journal
            journal = self.env['account.journal'].search([('type', '=', 'general')], limit=1)
            _logger.info(f"DEBUG: General Journal found: {journal}")
            
            if not journal:
                _logger.error("No general journal found. Cannot create landed cost.")
                return
            
            # Create Landed Cost
            if cost_lines_vals:
                _logger.info("DEBUG: Preparing to create Landed Cost")
                landed_cost = LandedCost.create({
                    'picking_ids': [(4, picking.id)],
                    'cost_lines': [(0, 0, val) for val in cost_lines_vals],
                    'account_journal_id': journal.id,
                })
                
                _logger.info(f"DEBUG: Landed Cost created with ID: {landed_cost.id}")
                
                # Compute and Validate Landed Cost
                try:
                    landed_cost.compute_landed_cost()
                    _logger.info("DEBUG: Landed cost computation completed")
                    
                    landed_cost.button_validate()
                    _logger.info("DEBUG: Landed cost validated successfully")
                except Exception as compute_error:
                    _logger.error(f"ERROR: Error in computing or validating landed cost: {compute_error}")
                    _logger.error(f"TRACEBACK: {traceback.format_exc()}")
                
                # Link Landed Cost to Purchase Order Line
                self.write({'landed_cost_ids': [(4, landed_cost.id)]})
                _logger.info(f"DEBUG: Landed Cost {landed_cost.id} linked to Purchase Order Line {self.id}")
        
        except Exception as e:
            _logger.error(f"CRITICAL ERROR: Unexpected error in creating landed cost: {e}")
            _logger.error(f"TRACEBACK: {traceback.format_exc()}")


class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'
    
    def _create_picking(self):
        _logger.info("DEBUG: Starting _create_picking method")
        pickings = super()._create_picking()
        
        if pickings and not isinstance(pickings, bool):
            _logger.info(f"DEBUG: Pickings created: {pickings}")
            for picking in pickings:
                _logger.info(f"DEBUG: Processing picking {picking.id}")
                for move in picking.move_ids:
                    _logger.info(f"DEBUG: Processing move {move.id}")
                    if move.purchase_line_id:
                        _logger.info(f"DEBUG: Calling _create_or_update_picking_landed_cost for move {move.id}")
                        move.purchase_line_id._create_or_update_picking_landed_cost(picking)
        
        return pickings
