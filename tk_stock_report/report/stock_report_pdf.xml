<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <template id="stock_report_template">
        <t t-call="web.html_container">
            <t t-call="web.internal_layout">
                <h2 style="text-align:center;">STOCK CARD REPORT</h2>
                <table style="border:solid 1px black;width:100%; table-layout: fixed;height:100%;">
                    <tr style="height:40px;background-color:#E5E4E2;">
                        <td style="width:30%;text-align:center;font-size:18px;vertical-align:middle;">Location</td>
                        <td style="width:30%;text-align:center;font-size:18px;vertical-align:middle;">Date</td>
                        <td style="width:30%;text-align:center;font-size:18px;vertical-align:middle;">Generated By</td>
                    </tr>
                    <tr style="height:40px;">
                        <td style="width:30%;text-align:center;font-size:16px;vertical-align:middle;">
                            <t t-esc="data['form_data']['stock_location_id'][1]"/>
                        </td>
                        <td style="width:30%;text-align:center;font-size:16px;vertical-align:middle;">
                            <t t-esc="data['form_data']['start_date']"/> To <t t-esc="data['form_data']['end_date']"/>
                        </td>
                        <td style="width:30%;text-align:center;font-size:16px;vertical-align:middle;">
                            <t t-esc="data['form_data']['create_uid'][1]"/>
                        </td>
                    </tr>
                </table>
                <div style="height:45px;margin-bottom:0px;"/>
                <table style="border:solid 1px black;width:100%; table-layout: fixed;height:100%;">
                    <tr style="height:20px;"/>
                    <tr style="height:40px;background-color:#E5E4E2;;">
                        <td style="text-align:center;font-size:18px;vertical-align:middle;" colspan="2">Date</td>
                        <td style="text-align:center;font-size:18px;vertical-align:middle;" colspan="2">Origin</td>
                        <td style="text-align:center;font-size:18px; vertical-align:middle;">In Quantity</td>
                        <td style="text-align:center;font-size:18px; vertical-align:middle;">Out Quantity</td>
                        <td style="text-align:center;font-size:18px; vertical-align:middle;">Balance</td>
                    </tr>
                    <t t-foreach="product_moves.items()" t-as="product">
                        <!-- Loop through each product and render its movement lines -->
                        <tr style="height:40px;margin-bottom:0px;">
                            <td style="vertical-align:middle;padding-left:5px;" colspan="7">
                                <strong style="font-size:16px;"><t t-esc="product[0]"/></strong> <!-- Product name -->
                            </td>
                        </tr>
                        <tr style="height:40px">
                            <td colspan="2" style="font-size:16px;vertical-align:middle;padding-left:5px;">
                            </td>
                            <td style="font-size:16px;vertical-align:middle;padding-left:5px;" colspan="2">
                                <strong>Opening Balance</strong>
                            </td>
                            <td style="font-size:16px;vertical-align:middle;padding-left:5px;">
                            </td>
                            <td style="font-size:16px;vertical-align:middle;padding-left:5px;">
                            </td>
                            <td style="font-size:16px;vertical-align:middle;padding-left:5px;">
                                <strong><t t-esc="product[1]['balance']"/></strong>
                            </td>
                        </tr>
                        <t t-foreach="product[1]['moves']" t-as="move_line">
                            <!-- Loop through each product's movements -->
                            <tr style="height:40px">
                                <td colspan="2" style="font-size:16px;vertical-align:middle;padding-left:5px;">
                                    <t t-esc="move_line['date']"/>
                                </td>
                                <td style="font-size:16px;vertical-align:middle;padding-left:5px;" colspan="2">
                                    <t t-esc="move_line['origin']"/>
                                </td>
                                <td style="font-size:16px;vertical-align:middle;padding-left:5px;">
                                    <t t-esc="move_line['in_qty']"/>
                                </td>
                                <td style="font-size:16px;vertical-align:middle;padding-left:5px;">
                                    <t t-esc="move_line['out_qty']"/>
                                </td>
                                <td style="font-size:16px;vertical-align:middle;padding-left:5px;">
                                    <t t-esc="move_line['balance']"/>
                                </td>
                            </tr>
                        </t>
                        <tr style="height:40px">
                            <td colspan="4" style="font-size:16px;vertical-align:middle;padding-left:5px;">
                                <strong>Total</strong>
                            </td>
                            <td style="font-size:16px;vertical-align:middle;padding-left:5px;">
                                <strong><t t-esc="product[1]['total_in']"/></strong>
                            </td>
                            <td style="font-size:16px;vertical-align:middle;padding-left:5px;">
                                <strong><t t-esc="product[1]['total_out']"/></strong>
                            </td>
                            <td style="font-size:16px;vertical-align:middle;padding-left:5px;">
                                <strong><t t-esc="product[1]['balance']"/></strong>
                            </td>
                        </tr>
                    </t>
                </table>
            </t>
        </t>
    </template>
    <!-- Report Action -->
    <record id="pdf_stock_report_action" model="ir.actions.report">
        <field name="name">Stock Report Card</field>
        <field name="model">stock.report.wizard</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">tk_stock_report.stock_report_template</field>
        <field name="report_file">tk_stock_report.stock_report_template</field>
        <field name="binding_model_id" ref="model_stock_report_wizard"/>
        <field name="binding_type">report</field>
    </record>
</odoo>
