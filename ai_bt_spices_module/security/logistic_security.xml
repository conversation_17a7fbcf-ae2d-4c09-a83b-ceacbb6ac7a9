<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Logistic Module Manager Group -->
        <record id="group_logistic_manager" model="res.groups">
            <field name="name">Logistic Manager</field>
            <field name="category_id" ref="base.module_category_inventory_inventory"/>
            <field name="implied_ids" eval="[(4, ref('stock.group_stock_user'))]"/>
        </record>

        <!-- Logistic Module User Group -->
        <record id="group_logistic_user" model="res.groups">
            <field name="name">Logistic User</field>
            <field name="category_id" ref="base.module_category_inventory_inventory"/>
            <field name="implied_ids" eval="[(4, ref('stock.group_stock_user'))]"/>
        </record>

        <!-- Access Rights for Logistic Module -->
        <record id="logistic_module_access_manager" model="ir.model.access">
            <field name="name">Logistic Module Access Manager</field>
            <field name="model_id" ref="model_logistic_module"/>
            <field name="group_id" ref="group_logistic_manager"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>

        <record id="logistic_module_access_user" model="ir.model.access">
            <field name="name">Logistic Module Access User</field>
            <field name="model_id" ref="model_logistic_module"/>
            <field name="group_id" ref="group_logistic_user"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="0"/>
        </record>
    </data>