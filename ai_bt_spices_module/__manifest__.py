{
    'name': 'AI BT Spices Module',
    'version': '1.0',
    'category': 'Manufacturing/Manufacturing',
    'summary': 'Advanced Product Profit Analysis for Manufacturing',
    'description': """
        This module adds advanced profit analysis features for manufacturing:
        * Track product profitability by lot
        * Monitor sales and costs
        * Dynamic pricing recommendations
        * Profit/Loss analysis
    """,
    'author': 'Your Name',
    'depends': [
        'base',
        'purchase',
        'stock',
        'mrp',
        'sale',
        'sale_management',
        'product',
        'base_automation',
        'hr',


         # Added for employee functionality
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/employee_profile_template.xml',  # Moved up since it's a QWeb report
        'views/mrp_production_view.xml',
        'views/mrp_production_list_view.xml',  # Added new view for list view with report_number
        'views/res_partner_view.xml',
        'views/automated_actions.xml',
        "views/add_bag_view.xml",
        "views/group.xml",
        'views/stock_valution_layer.xml',
        'views/account_move_view.xml',
        'views/stock_lot_view.xml',
        'views/purchase_inherit_view.xml',
        'views/purchase_order_line_color_view.xml',  # Added new view for colored purchase order lines
        'views/stock_quant_view.xml',
        'views/hr_employee_views.xml',
        'views/manufacturing_cost_report.xml',
        'views/product_product_stock_list.xml',
        'wizards/update_stock_move_rate_view.xml',
        'views/mrp_production_update_rate_view.xml'
# Added new view
        # 'views/product_profit_analysis_views.xml',
    ],
    'assets': {
        'web.assets_common': [
            'ai_bt_spices_module/static/images/bg.png',
        ],
    },
    'installable': True,
    'application': True,
    'auto_install': False,
    'license': 'LGPL-3',
    'post_init_hook': 'post_init_hook',
}
