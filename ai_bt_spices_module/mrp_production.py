from odoo import models, fields, api, _
from odoo.exceptions import UserError
from odoo.tools import float_round, float_compare
import logging

_logger = logging.getLogger(__name__)
class MrpProduction(models.Model):
    _inherit = 'mrp.production'

    # Weight Fields
    total_raw_material_weight = fields.Float('Total Raw Material Weight', compute='_compute_finished_weights', store=True)
    total_other_material_weight = fields.Float('Total Other Material Weight', compute='_compute_finished_weights', store=True)
    total_weight = fields.Float('Total Weight', compute='_compute_finished_weights', store=True)
    raw_material_weight_percentage = fields.Float('Raw Material Weight %', compute='_compute_finished_weights', store=True)
    other_material_weight_percentage = fields.Float('Other Material Weight %', compute='_compute_finished_weights', store=True)

    # Finished Product Weight Fields
    finished_goods_weight = fields.Float('Finished Goods Weight', compute='_compute_product_amounts' , store=True)
    reclean_goods_weight = fields.Float('Reclean Goods Weight', compute='_compute_product_amounts', store=True)
    crushing_goods_weight = fields.Float('Crushing Goods Weight', compute='_compute_product_amounts', store=True)
    wastage_goods_weight = fields.Float('Wastage Goods Weight', compute='_compute_product_amounts', store=True)
    missing_weight = fields.Float('Missing Weight', compute='_compute_finished_weights', store=True)
    total_finished_weight = fields.Float('Total Finished Weight', compute='_compute_finished_weights' , store=True)

    # Raw Material Costs
    kachi_raw_material_amount = fields.Float(string='Kachi Raw Material Amount', compute='_compute_material_costs', store=True)
    other_material_amount = fields.Float(string='Other Material Amount', compute='_compute_material_costs', store=True)
    total_material_cost = fields.Float(string='Total Material Cost', compute='_compute_material_costs', store=True)

    # Additional Costs
    hamali_cost = fields.Float(string='Hamali Cost', default=0.0 )
    sortex_landed_cost = fields.Float(string='Sortex Landed Cost', default=0.0)
    total_bag_cost = fields.Float(string='Total Bag Cost', default=0.0)
    total_additional_cost = fields.Float(string='Total Additional Cost', compute='_compute_additional_cost', store=True)

    # Final Calculations
    total_cost_with_kachi = fields.Float(string='Total Cost with Kachi', compute='_compute_final_costs', store=True)
    ci_landed_cost = fields.Float(string='CI Landed Cost', default=0.0)
    raw_material_grand_amount = fields.Float(string='Raw Material Grand Amount', compute='_compute_final_costs', store=True)
    per_kg_cost = fields.Float(string='RAW Material Per KG Cost', compute='_compute_final_costs', store=True)
    final_raw_material_cost = fields.Float(string='Final Raw Material Cost', compute='_compute_final_costs', store=True)
    total_final_cost = fields.Float(string='Total Final Cost', compute='_compute_final_costs', store=True)



    # Add these new fields
    finished_goods_amount = fields.Float(string='Finished Goods Amount', compute='_compute_product_amounts' , store=True )
    reclean_goods_amount = fields.Float(string='Reclean Goods Amount', compute='_compute_product_amounts', store=True )
    crushing_goods_amount = fields.Float(string='Crushing Goods Amount', compute='_compute_product_amounts', store=True )
    wastage_goods_amount = fields.Float(string='Wastage Goods Amount', compute='_compute_product_amounts', store=True )
    total_amount = fields.Float(string='Total Amount', compute='_compute_product_amounts', store=True)

    # Percentage fields for finished amounts
    finished_goods_percentage = fields.Float('Finished Goods Percentage', compute='_compute_finished_percentages', store=True)
    reclean_goods_percentage = fields.Float('Reclean Goods Percentage', compute='_compute_finished_percentages', store=True)
    crushing_goods_percentage = fields.Float('Crushing Goods Percentage', compute='_compute_finished_percentages', store=True)
    wastage_goods_percentage = fields.Float('Wastage Goods Percentage', compute='_compute_finished_percentages', store=True)
    missing_weight_percentage = fields.Float('Missing Weight Percentage', compute='_compute_finished_percentages', store=True)

    # Add missing field declaration if not already present
    total_sales_amount = fields.Float(string='Total Sales Amount', compute='_compute_sales_amount', store=True)
    total_production_cost = fields.Float(string='Total Production Cost', compute='_compute_production_cost', store=True)
    profit_loss = fields.Float(string='Profit/Loss', compute='_compute_profit_loss', store=True)
    recommended_price = fields.Float(string='Recommended Price', compute='_compute_recommended_price', store=True)

    # Add these new fields
    cost_share_finished = fields.Float('Finished Goods Cost Share %', compute='_compute_cost_share_percentages', store=True)
    cost_share_reclean = fields.Float('Reclean Goods Cost Share %', compute='_compute_cost_share_percentages', store=True)
    cost_share_crushing = fields.Float('Crushing Goods Cost Share %', compute='_compute_cost_share_percentages', store=True)
    cost_share_wastage = fields.Float('Wastage Goods Cost Share %', compute='_compute_cost_share_percentages', store=True)
    total_cost_share = fields.Float('Total Cost Share %', compute='_compute_cost_share_percentages', store=True)

    # Add these missing field declarations
    cost_per_kg = fields.Float(string='Cost per KG', compute='_compute_profit_loss', store=True)
    break_even_price = fields.Float(string='Break Even Price', compute='_compute_profit_loss', store=True)

    @api.depends('hamali_cost', 'sortex_landed_cost', 'total_bag_cost')
    def _compute_additional_cost(self):
        for record in self:
            if not record.id:
                continue
            record.total_additional_cost = (
                record.hamali_cost +
                record.sortex_landed_cost +
                record.total_bag_cost
            )

    @api.depends(
        'move_raw_material_ids.product_uom_qty',
        'move_raw_material_ids.state',
        'move_raw_material_ids.product_id.weight',
        'move_other_ids.product_uom_qty',
        'move_other_ids.state',
        'move_other_ids.product_id.weight',
        'move_finished_ids.product_uom_qty',
        'move_finished_ids.state',
        'move_finished_ids.product_id.weight',
        'move_finished_ids.x_product_total_weight',
        'move_finished_ids.sale_product',
        'move_finished_ids.riclin_product',
        'move_finished_ids.clean_product',
        'move_finished_ids.waste_product'
    )
    def _compute_finished_weights(self):
        for record in self:
            # Initialize all weight fields to 0.0 to ensure they're always set
            record.total_raw_material_weight = 0.0
            record.total_other_material_weight = 0.0
            record.total_weight = 0.0
            record.raw_material_weight_percentage = 0.0
            record.other_material_weight_percentage = 0.0
            record.finished_goods_weight = 0.0
            record.reclean_goods_weight = 0.0
            record.crushing_goods_weight = 0.0
            record.wastage_goods_weight = 0.0
            record.missing_weight = 0.0
            record.total_finished_weight = 0.0

            if not record.id:
                continue

            _logger.info('Computing finished weights for MO: %s', record.name)
            finished_moves = record.move_finished_ids.filtered(lambda m: m.state != 'draft')

            raw_weight = sum(
                move.product_uom_qty * move.product_id.weight
                for move in record.move_raw_material_ids.filtered(lambda m: m.state not in ('cancel', 'draft'))
            )

            # Calculate total weight from other materials
            other_weight = sum(
                move.product_uom_qty * move.product_id.weight
                for move in record.move_other_ids.filtered(lambda m: m.state not in ('cancel', 'draft'))
            )


            # Calculate  materials weights
            record.total_raw_material_weight = raw_weight
            record.total_other_material_weight = other_weight
            record.total_weight = raw_weight + other_weight
            _logger.info('********************Just Calculated Raw Weight %s',str(raw_weight))
            _logger.info('Just Calculated Other Weight %s',str(other_weight))
            _logger.info('Just Updated Total Weight %s',str(raw_weight + other_weight))

            # Calculate weight percentages
            if record.total_weight:
                record.raw_material_weight_percentage = (record.total_raw_material_weight / record.total_weight) * 100
                record.other_material_weight_percentage = (record.total_other_material_weight / record.total_weight) * 100
                _logger.info('Weight percentages - Raw: %s%%, Other: %s%%',
                            record.raw_material_weight_percentage,
                            record.other_material_weight_percentage)
            else:
                record.raw_material_weight_percentage = 0.0
                record.other_material_weight_percentage = 0.0
                _logger.warning('Total weight is zero, setting percentages to 0')

            # Calculate finished product weights
            finished_sale_moves = finished_moves.filtered(lambda m: m.sale_product)
            finished_riclin_moves = finished_moves.filtered(lambda m: m.riclin_product)
            finished_clean_moves = finished_moves.filtered(lambda m: m.clean_product)
            finished_waste_moves = finished_moves.filtered(lambda m: m.waste_product)

            # Log finished goods
            record.finished_goods_weight = sum(move.x_product_total_weight for move in finished_sale_moves)
            for move in finished_sale_moves:
                _logger.info('Finished Good: %s, Weight: %s',
                            move.product_id.name, move.x_product_total_weight)

            # Log reclean goods
            record.reclean_goods_weight = sum(move.x_product_total_weight for move in finished_riclin_moves)
            for move in finished_riclin_moves:
                _logger.info('Reclean Good: %s, Weight: %s',
                            move.product_id.name, move.x_product_total_weight)

            # Log crushing goods
            record.crushing_goods_weight = sum(move.x_product_total_weight for move in finished_clean_moves)
            for move in finished_clean_moves:
                _logger.info('Crushing Good: %s, Weight: %s',
                            move.product_id.name, move.x_product_total_weight)

            # Log wastage goods
            record.wastage_goods_weight = sum(move.x_product_total_weight for move in finished_waste_moves)
            for move in finished_waste_moves:
                _logger.info('Wastage Good: %s, Weight: %s',
                            move.product_id.name, move.x_product_total_weight)

            _logger.info('Summary of finished weights - Finished: %s, Reclean: %s, Crushing: %s, Wastage: %s',
                        record.finished_goods_weight,
                        record.reclean_goods_weight,
                        record.crushing_goods_weight,
                        record.wastage_goods_weight)

            record.missing_weight = record.total_weight - record.finished_goods_weight - record.reclean_goods_weight - record.crushing_goods_weight - record.wastage_goods_weight
            _logger.info('Missing weight: %s', record.missing_weight)

            # Calculate total finished weight
            record.total_finished_weight = (
                record.finished_goods_weight +
                record.reclean_goods_weight +
                record.crushing_goods_weight +
                record.wastage_goods_weight +
                record.missing_weight
            )
            _logger.info('Total finished weight: %s', record.total_finished_weight)

    @api.depends('move_raw_ids', 'move_finished_ids')
    def _compute_quantities(self):
        for record in self:
            if not record.id:
                continue
            # Available quantity from finished moves
            finished_moves = record.move_finished_ids.filtered(lambda m: m.state == 'done')
            record.available_quantity = sum(finished_moves.mapped('product_uom_qty'))

            # Sold quantity from related sale moves
            sold_moves = finished_moves.filtered(lambda m: m.sale_product)
            record.sold_quantity = sum(sold_moves.mapped('product_uom_qty'))

            # Calculate remaining stock
            record.remaining_stock = record.available_quantity - record.sold_quantity

            # Calculate total quantity from byproducts
            sale_moves = record.move_byproduct_ids.filtered(lambda m: m.sale_product)
            record.total_quantity = sum(move.product_uom_qty * move.product_id.weight
                                     for move in sale_moves)

    @api.depends('raw_material_cost', 'other_materials_cost', 'move_raw_ids', 'move_raw_ids.product_uom_qty', 'move_raw_ids.state', 'move_other_ids', 'move_other_ids.product_uom_qty', 'move_other_ids.state')
    def _compute_material_costs(self):
        _logger.info('>> _compute_material_costs triggered <<')
        for record in self:
            if not record.id:
                continue

            _logger.info('Computing material costs for MO: %s', record.name)
            _logger.info('Raw Material Cost from field: %s, Other Materials Cost from field: %s',
                         record.raw_material_cost, record.other_materials_cost)

            # Calculate Kachi Raw Material Amount
            record.kachi_raw_material_amount = record.raw_material_cost

            # Calculate Other Material Amount
            record.other_material_amount = record.other_materials_cost

            # Calculate Total Material Cost
            record.total_material_cost = record.kachi_raw_material_amount + record.other_material_amount

            _logger.info('Material costs calculated - Kachi: %s, Other: %s, Total: %s',
                         record.kachi_raw_material_amount,
                         record.other_material_amount,
                         record.total_material_cost)

    @api.depends('total_material_cost', 'total_additional_cost')
    def _compute_production_cost(self):
        for record in self:
            if not record.id:
                continue
            record.total_production_cost = record.total_material_cost + record.total_additional_cost


    @api.depends('move_finished_ids.party_moves.total_amount')
    def _compute_sales_amount(self):
        for record in self:
            if not record.id:
                continue

            _logger.info('Computing total sales amount for MO: %s', record.name)

            # Get all finished moves for this specific MRP order
            finished_moves = record.move_finished_ids.filtered(
                lambda m: m.state != 'cancel' and
                         m.production_id.id == record.id
            )

            total = 0.0
            for move in finished_moves:
                move_total = sum(party_move.total_amount for party_move in move.party_moves)
                _logger.info('Product: %s, Total Amount: %s',
                            move.product_id.name, move_total)
                total += move_total

            record.total_sales_amount = total
            _logger.info('Total sales amount for MO %s: %s',
                        record.name, total)

    @api.depends(
        'total_sales_amount',
    #     'total_final_cost',
    #     'reclean_goods_amount',
    #     'crushing_goods_amount',
    #     'wastage_goods_amount',
    #     'finished_goods_weight',
    #     'move_finished_ids.product_uom_qty',
    #     'move_finished_ids.state',
    #     'move_finished_ids.product_id.weight',
    #     'move_finished_ids.sale_product',
    #     'move_finished_ids.party_moves',
    #     'move_finished_ids.party_moves.weight_sold',
    #     'move_finished_ids.party_moves.total_amount',
    #     'move_byproduct_ids.product_uom_qty',
    #     'move_byproduct_ids.state',
    #     'move_byproduct_ids.product_id.weight',
    #     'total_amount'
    )
    def _compute_profit_loss(self):
        for record in self:
            if not record.id:
                continue

            _logger.info('Computing profit/loss for MO: %s', str(record.id))

            # Calculate total weight sold from party moves
            finished_moves = record.move_finished_ids.filtered(
                lambda m: m.state == 'done' and m.sale_product
            )

            weight_sold = sum(
                sum(party_move.weight_sold for party_move in move.party_moves)
                for move in finished_moves
            )

            _logger.info('Detailed weight calculation:')
            for move in finished_moves:
                move_weight = sum(party_move.weight_sold for party_move in move.party_moves)
                _logger.info('Move: %s , Party Moves Total Weight: %s', move.product_id.name, move_weight)

            # Ensure weight_sold is properly rounded
            weight_sold = float_round(weight_sold, precision_digits=3)

            _logger.info('---Computing profit/loss for MO: %s', str(record.id))
            _logger.info('---Total Raw Material Weight for MO: %s', str(record.total_raw_material_weight))
            # Calculate remaining weight to be sold (using exact weights)
            remaining_weight = float_round(record.finished_goods_weight - weight_sold, precision_digits=3)
            _logger.info('---Total Finished Goods Weight for MO: %s', str(record.finished_goods_weight))

            # Calculate base cost per kg with zero division check
            if record.total_weight and float_compare(record.total_weight, 0.0, precision_digits=3) > 0:
                cost_per_kg = record.total_final_cost / record.total_weight
            else:
                cost_per_kg = 0.0
                _logger.warning('Total weight is zero or negative for MO %s, setting cost_per_kg to 0', record.name)

            # Calculate break-even selling price (price needed for zero profit/loss)
            break_even_price = cost_per_kg

            # Calculate actual profit/loss based on current selling price
            if record.total_sales_amount > 0:
                actual_profit_loss = record.total_sales_amount - record.total_final_cost
            else:
                actual_profit_loss = record.total_amount - record.total_final_cost

            # Set recommended price slightly above break-even (e.g., 1% margin)
            recommended_price = break_even_price * 1.01

            # Store the computed values
            record.cost_per_kg = cost_per_kg
            record.profit_loss = actual_profit_loss
            record.recommended_price = recommended_price
            record.break_even_price = break_even_price

            _logger.info('Profit/Loss calculation details:')
            _logger.info('Total Sales Amount: %s', record.total_sales_amount)
            _logger.info('Total Final Cost: %s', record.total_final_cost)
            _logger.info('Total Weight: %s', record.finished_goods_weight)
            _logger.info('Weight Sold (from party moves): %s', weight_sold)
            _logger.info('Remaining Weight: %s', remaining_weight)
            _logger.info('Cost per KG: %s', cost_per_kg)
            _logger.info('Recommended Price: %s', recommended_price)
            _logger.info('Profit/Loss: %s', record.profit_loss)

    @api.depends('kachi_raw_material_amount', 'total_additional_cost', 'ci_landed_cost', 'move_raw_material_ids.product_uom_qty','total_raw_material_weight')
    def _compute_final_costs(self):
        for record in self:
            if not record.id:
                continue

            _logger.info('Computing final costs for MO: %s', record.name)

            # Calculate Total Cost with Kachi
            record.total_cost_with_kachi = record.kachi_raw_material_amount +  record.total_additional_cost
            _logger.info('Total Cost with Kachi: %s (Material Cost: %s, Additional Cost: %s)',
                        record.total_cost_with_kachi, record.kachi_raw_material_amount, record.total_additional_cost)

            # Calculate Raw Material Grand Amount
            record.raw_material_grand_amount = record.total_cost_with_kachi + record.ci_landed_cost
            _logger.info('Raw Material Grand Amount: %s (Total Cost with Kachi: %s, CI Landed Cost: %s)',
                        record.raw_material_grand_amount, record.total_cost_with_kachi, record.ci_landed_cost)

            # Use the already calculated total_raw_material_weight instead of recalculating
            # This prevents double-counting of other materials
            _logger.info('Using pre-calculated Total Raw Material Weight: %s', record.total_raw_material_weight)

            # Log individual move weights
            for move in record.move_raw_ids:
                if move.product_id and move.product_id.weight:
                    _logger.info('Move Weight Detail - Product: %s, Qty: %s, Weight/Unit: %s, Total: %s',
                                move.product_id.name, move.product_uom_qty,
                                move.product_id.weight,
                                move.product_uom_qty * move.product_id.weight)

            # Calculate Per KG Cost with zero division check
            if record.total_raw_material_weight and float_compare(record.total_raw_material_weight, 0.0, precision_digits=3) > 0:
                record.per_kg_cost = record.raw_material_grand_amount / record.total_raw_material_weight
            else:
                record.per_kg_cost = 0.0
                _logger.warning('Total raw material weight is zero or negative for MO %s, setting per_kg_cost to 0', record.name)

            _logger.info('Per KG Cost: %s (Raw Material Grand Amount: %s / total_raw_material_weight: %s)',
                        record.per_kg_cost, record.raw_material_grand_amount, record.total_raw_material_weight)

            # Set Final Raw Material Cost
            record.final_raw_material_cost = record.raw_material_grand_amount
            _logger.info('Final Raw Material Cost: %s', record.final_raw_material_cost)

            # Calculate Total Final Cost
            record.total_final_cost = record.final_raw_material_cost + record.other_material_amount
            _logger.info('Total Final Cost: %s', record.total_final_cost)

    @api.constrains('move_finished_ids')
    def _check_byproducts(self):
        _logger.info("Final cost share distribution before validation:")
        total_cost_share = 0

        for byproduct in self.move_byproduct_ids.filtered(lambda m: m.state not in ('cancel')):
            _logger.info("Byproduct: %s, Cost Share: %f%%", byproduct.product_id.name, byproduct.cost_share)
            total_cost_share += byproduct.cost_share

        total_cost_share = round(total_cost_share, 6)
        # Calculate difference from 100% for logging purposes
        total_cost_share1 = round(total_cost_share - 100, 6)
        _logger.info("Total cost share: %f%%, Difference from 100%%: %f%%", total_cost_share, total_cost_share1)

        if total_cost_share > 100.000000:
            _logger.error("Total by-product cost share exceeds 100%%: %f%%", total_cost_share)
            raise UserError(_("Total by-product cost share exceeds 100%%! Check by-product cost distribution."))






    # @api.depends(
    #     'move_byproduct_ids.product_uom_qty',
    #     'move_byproduct_ids.by_product_rate',
    #     'move_byproduct_ids.product_id.weight',
    #     'move_byproduct_ids.state',
    #     'move_byproduct_ids.sale_product',
    #     'move_byproduct_ids.riclin_product',
    #     'move_byproduct_ids.clean_product',
    #     'move_byproduct_ids.waste_product'
    # )
    def _compute_product_amounts(self):
        for record in self:
        # _logger.info("_compute_product_amounts " + str(len(self)))
        # if len(self) != 1:
        #     return  # Exit if not processing a single record
        # if True:
        #     # _logger.info("2 _compute_product_amounts " + str(len(self)))
        #     record = self
            # if not record.id:
            #     continue

            # Ensure it runs only for the current active record
            # _logger.info('Active ID: %s', self.env.context.get('active_id'))
            # if self.env.context.get('active_id') and self.env.context.get('active_id') != record.id:
            # if self.env.context.get('active_id') != record.id:
            #     continue
            # Initialize all fields to 0.0 first
            record.finished_goods_amount = 0.0
            record.reclean_goods_amount = 0.0
            record.crushing_goods_amount = 0.0
            record.wastage_goods_amount = 0.0
            record.total_amount = 0.0
            record.finished_goods_weight = 0.0
            record.reclean_goods_weight = 0.0
            record.crushing_goods_weight = 0.0
            record.wastage_goods_weight = 0.0


            # _logger.info('Active ID: %s', self.env.context.get('active_id'))
            # _logger.info('Computing product amounts for MO: %s - %s', record.name, str(record.id))

            for move in record.move_byproduct_ids.filtered(lambda m: m.state not in ('cancel', 'draft')):
                # Calculate total weight
                total_weight = move.product_uom_qty * (move.product_id.weight or 0.0)
                # Calculate amount using weight and rate
                amount = total_weight * (move.by_product_rate or 0.0)

                # Categorize based on product type
                if move.sale_product:
                    record.finished_goods_amount += amount
                    record.finished_goods_weight += total_weight
                elif move.riclin_product:
                    record.reclean_goods_amount += amount
                    record.reclean_goods_weight += total_weight
                elif move.clean_product:
                    record.crushing_goods_amount += amount
                    record.crushing_goods_weight += total_weight
                elif move.waste_product:
                    record.wastage_goods_amount += amount
                    record.wastage_goods_weight += total_weight

            record.total_amount = (record.finished_goods_amount + record.reclean_goods_amount +
                                 record.crushing_goods_amount + record.wastage_goods_amount)

    # @api.depends('total_weight', 'finished_goods_weight', 'reclean_goods_weight',
    #             'crushing_goods_weight', 'wastage_goods_weight', 'missing_weight')
    def _compute_finished_percentages(self):
        for record in self:
            # Initialize all percentage fields to 0.0
            record.finished_goods_percentage = 0.0
            record.reclean_goods_percentage = 0.0
            record.crushing_goods_percentage = 0.0
            record.wastage_goods_percentage = 0.0
            record.missing_weight_percentage = 0.0

            # Skip computation for new records
            if not record.id:
                continue

            # Calculate percentages only if total_weight is greater than 0
            if record.total_weight and float_compare(record.total_weight, 0.0, precision_digits=3) > 0:
                record.finished_goods_percentage = (record.finished_goods_weight / record.total_weight) * 100
                record.reclean_goods_percentage = (record.reclean_goods_weight / record.total_weight) * 100
                record.crushing_goods_percentage = (record.crushing_goods_weight / record.total_weight) * 100
                record.wastage_goods_percentage = (record.wastage_goods_weight / record.total_weight) * 100
                record.missing_weight_percentage = (record.missing_weight / record.total_weight) * 100

            _logger.info("Percentages for MO %s:", record.name)
            _logger.info("Finished Goods: %s%%", record.finished_goods_percentage)
            _logger.info("Reclean Goods: %s%%", record.reclean_goods_percentage)
            _logger.info("Crushing Goods: %s%%", record.crushing_goods_percentage)
            _logger.info("Wastage Goods: %s%%", record.wastage_goods_percentage)
            _logger.info("Missing Weight: %s%%", record.missing_weight_percentage)


    # @api.depends('move_finished_ids')
    # def _compute_waste_product_ids(self):
    #     for record in self:
    #         record.waste_product_ids = record.move_finished_ids.filtered(
    #             lambda m: m.state == 'done' and m.waste_product
    #         )

    # @api.depends('waste_product_ids', 'waste_product_ids.total_cost')
    # def _compute_waste_costs(self):
    #     for record in self:
    #         record.waste_product_cost = sum(record.waste_product_ids.mapped('total_cost') or [0.0])

    # @api.depends('move_byproduct_ids', 'move_byproduct_ids.cost_share')
    def _compute_cost_share_percentages(self):
        for record in self:
            # Initialize values
            record.cost_share_finished = 0.0
            record.cost_share_reclean = 0.0
            record.cost_share_crushing = 0.0
            record.cost_share_wastage = 0.0
            record.total_cost_share = 0.0

            if not record.id:
                continue

            for move in record.move_byproduct_ids.filtered(lambda m: m.state not in ('cancel')):
                if move.sale_product:
                    record.cost_share_finished += move.cost_share
                elif move.riclin_product:
                    record.cost_share_reclean += move.cost_share
                elif move.clean_product:
                    record.cost_share_crushing += move.cost_share
                elif move.waste_product:
                    record.cost_share_wastage += move.cost_share

            record.total_cost_share = (
                record.cost_share_finished +
                record.cost_share_reclean +
                record.cost_share_crushing +
                record.cost_share_wastage
            )

            _logger.info('Cost Share Percentages for MO %s:', record.name)
            _logger.info('Finished: %s%%', record.cost_share_finished)
            _logger.info('Reclean: %s%%', record.cost_share_reclean)
            _logger.info('Crushing: %s%%', record.cost_share_crushing)
            _logger.info('Wastage: %s%%', record.cost_share_wastage)
            _logger.info('Total: %s%%', record.total_cost_share)



class ManufacturingCostReport(models.Model):
    _name = 'manufacturing.cost.report'
    _description = 'Manufacturing Cost Details Report'
    _auto = False  # This is a read-only report model

    production_id = fields.Many2one('mrp.production', string='Manufacturing Order')
    product_id = fields.Many2one('product.product', string='Product')
    hamali_cost = fields.Float(string='Hamali Cost')
    sortex_landed_cost = fields.Float(string='Sortex Landed Cost')
    ci_landed_cost = fields.Float(string='CI Landed Cost')
    total_bag_cost = fields.Float(string='Total Bag Cost')

    def init(self):
        """
        Initialize the SQL view for the report
        """
        self.env.cr.execute("""
            CREATE OR REPLACE VIEW manufacturing_cost_report AS (
                SELECT
                    mo.id AS id,
                    mo.id AS production_id,
                    mo.product_id AS product_id,
                    mo.hamali_cost AS hamali_cost,
                    mo.sortex_landed_cost AS sortex_landed_cost,
                    mo.ci_landed_cost AS ci_landed_cost,
                    mo.total_bag_cost AS total_bag_cost,

                FROM
                    mrp_production mo
            )
        """)

    @api.model
    def read_group(self, domain, fields, groupby, offset=0, limit=None, orderby=False, lazy=True):
        """
        Override read_group to allow aggregations on custom fields
        """
        return super().read_group(
            domain, fields, groupby, offset=offset,
            limit=limit, orderby=orderby, lazy=lazy
        )