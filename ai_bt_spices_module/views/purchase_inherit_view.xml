<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="purchase_order_form_inherit" model="ir.ui.view">
        <field name="name">purchase.order.form.inherit</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='picking_type_id']" position="replace">
                <field name="picking_type_id" invisible="1"/>
            </xpath>
            
            
            
            <xpath expr="//field[@name='order_line']/list//field[@name='x_bag_quantity']" position="after">
                <!-- <field name="x_bag"/>
                <field name="x_bag_quantity"/> -->
                <!-- <field name="x_bag_cost" optional="show"/> -->
                <field name="x_transport_cost" optional="hide" readonly="1"/>
                <field name="x_thekedar_cost" optional="hide" readonly="1"/>
                <field name="x_additional_cost" optional="hide"/>
            </xpath>
            
        

        
        </field>
    </record>
</odoo>
