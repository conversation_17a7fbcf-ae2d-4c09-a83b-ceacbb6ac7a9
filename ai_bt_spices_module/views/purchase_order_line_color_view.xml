<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>
        <!-- Inherit the purchase order form view to add decoration to order lines -->
        <record id="purchase_order_form_line_color_inherit" model="ir.ui.view">
            <field name="name">purchase.order.form.line.color.inherit</field>
            <field name="model">purchase.order</field>
            <field name="inherit_id" ref="purchase.purchase_order_form"/>
            <field name="arch" type="xml">
                <!-- Add decoration attributes to the order_line list view -->
                <xpath expr="//field[@name='order_line']/list" position="attributes">
                    <attribute name="decoration-info">product_is_bag == True</attribute>
                    <attribute name="decoration-warning">product_type == 'service' and not product_is_bag</attribute>
                    <attribute name="decoration-success">product_type == 'consu' and not product_is_bag</attribute>
                </xpath>

                <!-- Add invisible fields to make the decoration work -->
                <xpath expr="//field[@name='order_line']/list/field[@name='product_id']" position="after">
                    <field name="product_is_bag" invisible="1" optional="hide" readonly="1"/>
                    <field name="product_type" invisible="1" optional="hide" readonly="1"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
