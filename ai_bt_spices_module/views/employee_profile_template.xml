<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="employee_id_card_report" model="ir.actions.report">
        <field name="name">Employee ID Card</field>
        <field name="model">hr.employee</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">ai_bt_spices_module.employee_id_card_template</field>
        <field name="report_file">ai_bt_spices_module.employee_id_card_template</field>
        <field name="binding_model_id" ref="hr.model_hr_employee"/>
        <field name="binding_type">report</field>
    </record>

    <template id="employee_id_card_template">
         <t t-call="web.html_container">
            <t t-foreach="docs" t-as="employee">
                <div class="page" style= "padding: 20px;">
                    <style>
                        body {
                            <t t-att-style="'background: url(/web/static/src/img/bg.png) no-repeat center center fixed; background-size: cover;'"/>
                            background-size: cover;
                            margin: 0;
                            padding: 0;
                        }
                        .page {
                            page-break-after: always;
                            width: 100%;
                            max-width: 400px;
                            margin: 0 auto;
                            position: relative;
                        }
                        .page:last-child {
                            page-break-after: avoid;
                        }
                        .id-card {
                            width: 100%;
                            padding: 20px;
                            text-align: center;
                            background-color: white;
                            border: 1px solid #ddd;
                            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                            position: relative;
                        }
                        .company-header {
                            margin-bottom: 30px;
                        }
                        .company-logo {
                            max-width: 200px;
                            max-height: 150px;
                            margin-bottom: 15px;
                        }
                        .company-name {
                            margin: 10px 0;
                            font-size: 24px;
                            white-space: nowrap;
                        }
                        .company-name-bhailal {
                            color: #8B1F3F;
                        }
                        .company-name-trikamlal {
                            color: #B87D4B;
                        }
                        .company-since {
                            font-size: 14px;
                            margin-bottom: 20px;
                            color: #666;
                        }
                        .photo-container {
                            width: 150px;
                            height: 150px;
                            margin: 0 auto 20px;
                            background-color: #673AB7;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            overflow: hidden;
                        }
                        .photo-placeholder {
                            color: white;
                            font-size: 72px;
                        }
                        .employee-photo {
                            width: 100%;
                            height: 100%;
                            border-radius: 50%;
                            object-fit: cover;
                        }
                        .employee-name {
                            font-size: 20px;
                            font-weight: bold;
                            margin-bottom: 10px;
                            display: block;
                        }
                        .employee-title {
                            font-size: 18px;
                            margin-bottom: 20px;
                        }
                        .details-container {
                            max-width: 300px;
                            margin: 0 auto;
                            text-align: left;
                        }
                        .detail-row {
                            margin-bottom: 10px;
                            display: flex;
                            align-items: center;
                        }
                        .detail-label {
                            font-weight: bold;
                            margin-right: 10px;
                            width: 80px;
                            flex-shrink: 0;
                        }
                        .detail-value {
                            flex-grow: 1;
                            min-height: 20px;
                        }
                        .barcode-section {
                            margin-top: 20px;
                            text-align: center;
                        }
                        .barcode {
                            width: 200px;
                            height: 40px;
                            margin: 0 auto;
                        }
                        .barcode img {
                            max-width: 100%;
                            height: auto;
                        }
                        .back-side {
                            width: 100%;
                            height: 50%;
                         
                            position: relative;
                            display: flex;
                            flex-direction: column;
                            justify-content: center;
                            align-items: center;
                            padding: 0;
                            margin: 0;
                        <!--}-->
                        .back-content {
                            text-align: center;
                            position: absolute;
                            bottom: 50px;
                            width: 100%;
                        }
                        .back-logo {
                            max-width: 250px;
                            max-height: 250px;
                            margin-bottom: 20px;
                        }
                        .company-name {
                            font-size: 24px;
                            margin-bottom: 10px;
                        }
                        .company-name-bhailal {
                            color: #8B1F3F;
                        }
                        .company-name-trikamlal {
                            color: #B87D4B;
                        }
                        .company-since {
                            font-size: 16px;
                            color: #666;
                        }
                    </style>

                    <!-- Front Side -->
                    <div class="id-card front-side">
                        <div class="company-header">
                            <img t-if="res_company.logo" t-att-src="image_data_uri(res_company.logo)" class="company-logo" alt="Company Logo"/>
                            
                            <div class="company-name">
                                <span class="company-name-bhailal">BHAILAL</span> 
                                <span class="company-name-trikamlal">TRIKAMLAL</span> &amp;<span class="company-name-trikamlal"> CO.</span>
                            </div>
                            <div class="company-since">SINCE 1962</div>
                        </div>

                        <div class="photo-container">
                            <t t-if="employee.image_1920">
                                <img t-att-src="image_data_uri(employee.image_1920)" class="employee-photo" alt="Employee Photo"/>
                            </t>
                            <t t-else="">
                                <div class="photo-placeholder">A</div>
                            </t>
                        </div>
                        <span class="employee-name" t-field="employee.name"/>

                        <div class="employee-title" t-field="employee.job_title" style="color: #B87D4B;"/>

                        <div class="details-container">
                            <div class="detail-row">
                                <span class="detail-label">DOB:</span>
                                <span class="detail-value" t-field="employee.birthday" t-options="{'widget': 'date'}"/>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">Blood Group:</span>
                                <span class="detail-value" t-field="employee.blood_group"/>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">Phone:</span>
                                <span class="detail-value" t-esc="employee.work_phone or '-'"/>
                            </div>
                        </div>

                        <div class="barcode-section">
                            <div class="barcode">
                                <div t-if="employee.barcode" t-field="employee.barcode" t-options="{'widget': 'barcode', 'width': 600, 'height': 120, 'img_style': 'max-width:100%;', 'img_align': 'center'}" data-oe-demo="12345678901"/>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Back Side -->
                <div class="page" style="padding: 20px;">
                    <div class="id-card back-side">
                        <!-- Company Logo -->
                        <div class="back-logo-container">
                            <img t-if="res_company.logo" t-att-src="image_data_uri(res_company.logo)" class="back-logo" alt="Company Logo"/>
                        </div>

                        <!-- Company Header at Bottom -->
                        <div class="back-company-header">
                            <div class="back-company-name">
                                <span class="company-name-bhailal">BHAILAL</span> 
                                <span class="company-name-trikamlal">TRIKAMLAL</span> &amp; CO.
                            </div>
                            <div class="back-company-since">SINCE 1962</div>
                        </div>
                    </div>
                </div>
            </t>
        </t>
    </template>
</odoo>