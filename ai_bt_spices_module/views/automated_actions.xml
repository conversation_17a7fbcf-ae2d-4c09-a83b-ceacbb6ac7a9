<?xml version="1.0"?>
<odoo>

    <data>
        <!-- Automated Action for Creating Landed Cost -->
<!--        <record id="1" model="ir.actions.server">-->
<!--            <field name="name">Create Landed Cost</field>-->
<!--            <field name="model_id" ref="purchase.model_purchase_order"/>-->
<!--            <field name="state">code</field>-->
<!--            <field name="code">-->
<!--record.create_landed_cost()-->
<!--            </field>-->
<!--        </record>-->

<!--        &lt;!&ndash; Automated Action Trigger on Purchase Order Confirmation &ndash;&gt;-->
<!--        <record id="automated_action_create_landed_cost" model="base.automation">-->
<!--            <field name="name">Create Landed Cost on Purchase Order Confirmation</field>-->
<!--            <field name="model_id" ref="purchase.model_purchase_order"/>-->
<!--            <field name="trigger">on_write</field>-->
<!--            <field name="filter_domain">[('state', '=', 'purchase')]</field>-->
<!--            <field name="action_server_ids" eval="[(4, ref('1'))]"/>-->
<!--            <field name="active" eval="True"/>-->
<!--        </record>-->


        <record id="2" model="ir.actions.server">
            <field name="name">create vendor warehouse</field>
            <field name="model_id" ref="base.model_res_partner"/>
            <field name="state">code</field>
            <field name="code">

    # Create a new stock location
new_location = record.create_vendor_warehouse()

    # Assign the new location to the vendor
record.update_vendor_location()
            </field>
        </record>

        <!-- Automated Action Trigger on Purchase Order Confirmation -->
        <record id="automated_action_create_vendor_warehouse" model="base.automation">
            <field name="name">create vendor warehouse</field>
            <field name="model_id" ref="base.model_res_partner"/>
            <field name="trigger">on_create</field>
            <field name="action_server_ids" eval="[(4, ref('2'))]"/>
            <field name="active" eval="True"/>
        </record>


        <record id="4" model="ir.actions.server">
            <field name="name">Auto-select Vendor Warehouse Location</field>
            <field name="model_id" ref="mrp.model_mrp_production"/>
            <field name="state">code</field>
            <field name="code">
                <![CDATA[
records.auto_select_vendor_warehouse()
        ]]>
            </field>
        </record>

        <record id="action_auto_select_vendor_location" model="base.automation">
            <field name="name">Auto-select Vendor Warehouse Location Automated</field>
            <field name="model_id" ref="mrp.model_mrp_production"/>
            <field name="trigger">on_write</field>
            <field name="action_server_ids" eval="[(4, ref('4'))]"/>
            <field name="active" eval="True"/>
        </record>

        <!-- <record id="5" model="ir.actions.server">
            <field name="name">create manufacturing landed cost</field>
            <field name="model_id" ref="mrp.model_mrp_production"/>
            <field name="state">code</field>
            <field name="code">
if record.state == 'done':
    LandedCost = env['stock.landed.cost']
    LandedCostLine = env['stock.landed.cost.lines']
    Product = env['product.product']

    # Search for existing Landed Cost records to avoid duplication
    existing_landed_costs = LandedCost.search([
        ('mrp_production_ids', 'in', [record.id]),
        ('name', 'in', [
            f'Landed Cost for MO {record.name} - Initial',
            f'Landed Cost for MO {record.name} - Additional 4.5%'
        ])
    ])
    
    existing_names = existing_landed_costs.mapped('name')

    # Create the first Landed Cost record if it doesn't already exist
    if f'Landed Cost for MO {record.name} - Initial' not in existing_names:
        landed_cost_1 = LandedCost.create({
            'name': f'Landed Cost for MO {record.name} - Initial',
            'target_model': 'manufacturing',
            'mrp_production_ids': [(6, 0, [record.id])],
        })
        
        # Get products for labour cost and cleaning charge
        labour_product = Product.search([('name', '=', 'Labour Cost')], limit=1)
        cleaning_product = Product.search([('name', '=', 'Cleaning Charge')], limit=1)
        
        if labour_product and cleaning_product:
            # Add Labour Cost to the first Landed Cost record
            LandedCostLine.create({
                'cost_id': landed_cost_1.id,
                'product_id': labour_product.id,
                'price_unit': 100,  # Example cost, adjust as needed
                'split_method':'by_weight',
            })
            
            # Add Cleaning Charge to the first Landed Cost record
            LandedCostLine.create({
                'cost_id': landed_cost_1.id,
                'product_id': cleaning_product.id,
                'price_unit': 50,  # Example cost, adjust as needed
                'split_method':'by_weight',
            })
            
            # Optionally, validate the first Landed Cost record
            landed_cost_1.button_validate()
    
    # Find the first Landed Cost record to copy lines from
    landed_cost_1 = LandedCost.search([
        ('mrp_production_ids', 'in', [record.id]),
        ('name', '=', f'Landed Cost for MO {record.name} - Initial')
    ], limit=1)
    
    if landed_cost_1:
        # Retrieve lines from the first Landed Cost record
        lines = LandedCostLine.search([('cost_id', '=', landed_cost_1.id)])
        
        # Create the second Landed Cost record if it doesn't already exist
        if f'Landed Cost for MO {record.name} - Additional 4.5%' not in existing_names:
            landed_cost_2 = LandedCost.create({
                'name': f'Landed Cost for MO {record.name} - Additional 4.5%',
                'target_model': 'manufacturing',
                'mrp_production_ids': [(6, 0, [record.id])],
            })
            
            # Add the same products to the second Landed Cost record with a 4.5% increase in cost
            for line in lines:
                # Calculate the new price with a 4.5% increase
                new_price_unit = line.price_unit * 1.045
                
                # Create Landed Cost Line for the second record
                LandedCostLine.create({
                    'cost_id': landed_cost_2.id,
                    'product_id': line.product_id.id,
                    'price_unit': new_price_unit,
                    'split_method': line.split_method,
                })
            
            # Optionally, validate the second Landed Cost record
            landed_cost_2.button_validate()
            </field>
        </record> -->

        <!-- Automated Action Trigger on Purchase Order Confirmation -->
        <!-- <record id="automated_action_create_manufacturing_landed_cost" model="base.automation">
            <field name="name">create manufacturing landed cost automation</field>
            <field name="model_id" ref="mrp.model_mrp_production"/>
            <field name="trigger">on_write</field>
            <field name="filter_domain">[('state', '=', 'done')]</field>
            <field name="action_server_ids" eval="[(4, ref('5'))]"/>
            <field name="active" eval="True"/>
        </record> -->
    </data>

</odoo>
