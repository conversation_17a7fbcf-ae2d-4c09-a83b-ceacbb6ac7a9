from odoo import models, fields, api, _
import math
class StockQuant(models.Model):
    _inherit = 'stock.quant'

    lot_weight = fields.Float(
        string='Total Weight',
        compute='_compute_lot_fields',
        store=True,
        readonly=True,
        help='Total weight based on quantity and product weight'
    )
    
    lot_avg_cost_per_kg = fields.Float(
        string='Avg Cost/Kg',
        compute='_compute_lot_fields',
        store=True,
        readonly=True,
        help='Average cost per kg from the associated lot'
    )

        
    inventory_quantity_ceil = fields.Float(
        string='Rounded Quantity',
        compute='_compute_inventory_quantity_ceil',
        store=False,
        readonly=True
    )
    
    @api.depends('inventory_quantity_auto_apply')
    def _compute_inventory_quantity_ceil(self):
        for quant in self:
            quant.inventory_quantity_ceil = math.ceil(quant.inventory_quantity_auto_apply) if quant.inventory_quantity_auto_apply else 0

  
    @api.depends('product_id', 'product_id.weight', 'lot_id.avg_cost_per_weight', 'quantity')
    def _compute_lot_fields(self):
        for quant in self:
            if quant.product_id:
                # Calculate total weight by multiplying quantity with product weight
                quant.lot_weight = (quant.product_id.weight * quant.quantity) if quant.product_id.weight else 0.0
                quant.lot_avg_cost_per_kg = quant.lot_id.avg_cost_per_weight if quant.lot_id.avg_cost_per_weight else 0.0
            else:
                quant.lot_weight = 0.0
                quant.lot_avg_cost_per_kg = 0.0
                
              

    