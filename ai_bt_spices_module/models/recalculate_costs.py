from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)

class MrpProduction(models.Model):
    _inherit = 'mrp.production'

    def update_other_material_amount_directly(self):
        """Directly update other_material_amount based on the sum of total_cost for other materials.

        This method is a fallback for when the computed field is not updating properly.
        It calculates other_material_amount directly and updates it with write().
        """
        self.ensure_one()

        _logger.info("=== Starting direct update of other_material_amount for MO %s ===", self.name)

        try:
            # Get all other material moves that are done
            other_moves = self.move_other_ids.filtered(lambda m: m.state == 'done')

            # Log each move's total_cost contribution to the sum
            _logger.info("DIRECT_UPDATE: Summing up total_cost values for other_material_amount:")
            for m in other_moves:
                _logger.info("DIRECT_UPDATE: Move ID: %s, Product: %s, actual_cost: %s, total_cost: %s, lot_ids: %s",
                           m.id, m.product_id.name, m.actual_cost, m.total_cost, m.lot_ids.mapped('name'))

            # Calculate the sum of total_cost for all other material moves
            other_materials_sum = sum(m.total_cost for m in other_moves)

            # Log the calculation
            _logger.info("DIRECT_UPDATE: Calculated other_material_amount: %s (current: %s)",
                       other_materials_sum, self.other_material_amount)

            # Update other_material_amount and other_materials_cost directly
            if other_materials_sum != self.other_material_amount:
                self.write({
                    'other_material_amount': other_materials_sum,
                    'other_materials_cost': other_materials_sum
                })
                _logger.info("DIRECT_UPDATE: Updated other_material_amount to %s", other_materials_sum)

            # Recalculate dependent fields
            self.invalidate_recordset(['total_material_cost', 'total_final_cost', 'profit_loss'])

            # Update total_material_cost
            total_material_cost = self.kachi_raw_material_amount + other_materials_sum
            if total_material_cost != self.total_material_cost:
                self.write({'total_material_cost': total_material_cost})
                _logger.info("DIRECT_UPDATE: Updated total_material_cost to %s", total_material_cost)

            # Update total_final_cost
            total_final_cost = self.final_raw_material_cost + other_materials_sum
            if total_final_cost != self.total_final_cost:
                self.write({'total_final_cost': total_final_cost})
                _logger.info("DIRECT_UPDATE: Updated total_final_cost to %s", total_final_cost)

            # Update profit_loss
            profit_loss = self.total_sales_amount - total_final_cost
            if profit_loss != self.profit_loss:
                self.write({'profit_loss': profit_loss})
                _logger.info("DIRECT_UPDATE: Updated profit_loss to %s", profit_loss)

            _logger.info("=== Direct update of other_material_amount completed successfully for MO %s ===", self.name)

            return True

        except Exception as e:
            _logger.error("Error in direct update of other_material_amount for MO %s: %s", self.name, str(e), exc_info=True)
            raise UserError(_("Error in direct update of other_material_amount: %s") % str(e))

    def recalculate_all_costs(self):
        """Comprehensive method to recalculate all cost-related fields in the correct sequence.

        This method ensures that all dependent fields are recalculated in the proper order:
        1. First, recalculate weights and material costs
        2. Then, recalculate final costs
        3. Finally, recalculate profit/loss

        This method can be called from both the Update Stock Move Rate wizard and the button_mark_done method.
        """
        self.ensure_one()

        _logger.info("=== Starting comprehensive cost recalculation for MO %s ===", self.name)

        try:
            # Step 1: Recalculate weights
            _logger.info("Step 1: Recalculating weights")
            self._compute_total_weight()
            _logger.info("Total weight after recalculation: %s", self.total_weight)

            # Step 2: Recalculate hamali and sortex costs (depend on total_weight)
            _logger.info("Step 2: Recalculating hamali and sortex costs")
            self._compute_hamali_cost()
            self._compute_sortex_landed_cost()
            _logger.info("Hamali cost after recalculation: %s", self.hamali_cost)
            _logger.info("Sortex landed cost after recalculation: %s", self.sortex_landed_cost)

            # Step 3: Recalculate material costs
            _logger.info("Step 3: Recalculating material costs")
            # Force recalculation of total_cost for all other material moves
            other_moves = self.move_other_ids.filtered(lambda m: m.state == 'done')
            for m in other_moves:
                if m.lot_ids:
                    _logger.info("Move %s has lot_ids, forcing recalculation of actual_cost and total_cost", m.id)
                    m.invalidate_recordset(['actual_cost', 'total_cost'])

            # Now recalculate material costs
            self._compute_material_costs()
            _logger.info("Material costs after recalculation - Kachi: %s, Other: %s, Total: %s",
                        self.kachi_raw_material_amount, self.other_material_amount, self.total_material_cost)

            # If other_material_amount is still 0 but there are other materials with lot numbers,
            # try a direct update
            if self.other_material_amount <= 0 and any(m.lot_ids for m in other_moves):
                _logger.warning("other_material_amount is still 0 but there are other materials with lot numbers, trying direct update")
                self.update_other_material_amount_directly()

            # Step 4: Recalculate CI landed cost (depends on material costs)
            _logger.info("Step 4: Recalculating CI landed cost")
            self._compute_ci_landed_cost()
            _logger.info("CI landed cost after recalculation: %s", self.ci_landed_cost)

            # Step 5: Recalculate final costs
            _logger.info("Step 5: Recalculating final costs")
            self._compute_final_costs()
            _logger.info("Total final cost after recalculation: %s", self.total_final_cost)

            # Step 6: Recalculate product amounts (for byproducts)
            _logger.info("Step 6: Recalculating product amounts")
            self._compute_product_amounts()
            _logger.info("Total sales amount after recalculation: %s", self.total_sales_amount)

            # Step 7: Recalculate profit/loss
            _logger.info("Step 7: Recalculating profit/loss")
            self._compute_profit_loss()
            _logger.info("Profit/loss after recalculation: %s", self.profit_loss)

            # Step 8: Recalculate percentages
            _logger.info("Step 8: Recalculating percentages")
            self._compute_finished_percentages()
            self._compute_cost_share_percentages()

            # Invalidate cache to ensure UI is updated
            self.env.invalidate_all()

            _logger.info("=== Comprehensive cost recalculation completed successfully for MO %s ===", self.name)

            return True

        except Exception as e:
            _logger.error("Error in comprehensive cost recalculation for MO %s: %s", self.name, str(e), exc_info=True)
            raise UserError(_("Error in comprehensive cost recalculation: %s") % str(e))

    def button_recalculate_weight_analysis(self):
        """Recalculate all weight-related fields for the manufacturing order."""
        self.ensure_one()

        _logger.info("=== Starting weight analysis recalculation for MO %s ===", self.name)

        try:
            # Use the comprehensive recalculation method
            self.recalculate_all_costs()

            _logger.info("=== Weight analysis recalculation completed successfully for MO %s ===", self.name)

            # Show success message to user
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success'),
                    'message': _('Weight analysis and costs recalculated successfully.'),
                    'sticky': False,
                    'type': 'success',
                }
            }

        except Exception as e:
            _logger.error("Error in weight analysis recalculation for MO %s: %s", self.name, str(e), exc_info=True)

            # Show error message to user
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('Failed to recalculate weight analysis: %s') % str(e),
                    'sticky': True,
                    'type': 'danger',
                }
            }
