# models/party_details_wizard.py

from odoo import models, fields, api, _
from odoo.exceptions import UserError

class PartyDetailsWizard(models.TransientModel):
    _name = 'party.details.wizard'
    _description = 'Party Details Wizard'

    # Using Many2many as we might want to add party details to multiple selected records
    stock_move_id = fields.Many2one('stock.move', string='Stock Move', required=True)
    party_line_ids = fields.One2many('party.details.wizard.line', 'wizard_id', string='Party Lines')
    total_weight = fields.Float(string='Total Weight', compute='_compute_total_weight', store=True)
    remaining_weight = fields.Float(string='Remaining Weight', compute='_compute_remaining_weight', store=True)
    move_quantity = fields.Float(string='Move Quantity', related='stock_move_id.product_uom_qty', readonly=True)

    @api.depends('party_line_ids.weight_sold')
    def _compute_total_weight(self):
        for wizard in self:
            wizard.total_weight = sum(line.weight_sold for line in wizard.party_line_ids)

    @api.depends('total_weight', 'move_quantity')
    def _compute_remaining_weight(self):
        for wizard in self:
            wizard.remaining_weight = wizard.move_quantity - wizard.total_weight

    @api.model
    def default_get(self, fields_list):
        res = super(PartyDetailsWizard, self).default_get(fields_list)
        if self._context.get('active_id'):
            stock_move = self.env['stock.move'].browse(self._context.get('active_id'))
            res['stock_move_id'] = stock_move.id
            
            # Pre-fill existing party details if any
            if stock_move.party_moves:
                party_lines = []
                for party in stock_move.party_moves:
                    party_lines.append((0, 0, {
                        'customer': party.customer.id,
                        'bag_type': party.bag_type.id,
                        'bag_price': party.bag_price,
                        'weight_sold': party.weight_sold,
                        'sell_cost': party.sell_cost,
                    }))
                res['party_line_ids'] = party_lines
        return res

    def action_confirm(self):
        self.ensure_one()
        if self.total_weight > self.move_quantity:
            raise UserError(_("Total weight cannot exceed the move quantity."))

        # Clear existing party moves
        self.stock_move_id.party_moves.unlink()

        # Create new party moves
        for line in self.party_line_ids:
            self.env['party.id'].create({
                'customer': line.customer.id,
                'bag_type': line.bag_type.id,
                'bag_price': line.bag_price,
                'weight_sold': line.weight_sold,
                'sell_cost': line.sell_cost,
                'stock_move_id': self.stock_move_id.id,
            })

        return {'type': 'ir.actions.act_window_close'}

class PartyDetailsWizardLine(models.TransientModel):
    _name = 'party.details.wizard.line'
    _description = 'Party Details Wizard Line'

    wizard_id = fields.Many2one('party.details.wizard', string='Wizard')
    customer = fields.Many2one('res.partner', string='Customer', required=True)
    bag_type = fields.Many2one('product.product', string='Bag Type', 
                              domain=[('x_is_bag', '=', True)], required=True)
    bag_price = fields.Float(string='Bag Price', readonly=False)
    weight_sold = fields.Float(string='Weight Sold', required=True)
    sell_cost = fields.Float(string='Sell Cost', required=True)
    total_amount = fields.Float(string='Total Amount', compute='_compute_total_amount', store=True)

    @api.depends('weight_sold', 'sell_cost')
    def _compute_total_amount(self):
        for line in self:
            line.total_amount = line.weight_sold * line.sell_cost

    @api.onchange('bag_type')
    def _onchange_bag_type(self):
        if self.bag_type:
            self.bag_price = self.bag_type.list_price
        else:
            self.bag_price = 0.0