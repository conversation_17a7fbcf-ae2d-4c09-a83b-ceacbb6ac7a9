<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_update_stock_move_rate_form" model="ir.ui.view">
        <field name="name">update.stock.move.rate.form</field>
        <field name="model">update.stock.move.rate</field>
        <field name="arch" type="xml">
            <form string="Update Stock Move Rate">
                <sheet>
                    <group>
                        <group>
                            <field name="production_id" invisible="1"/>
                            <field name="move_id" options="{'no_create': True, 'no_open': True}"/>
                            <field name="product_id"/>
                            <field name="lot_ids" widget="many2many_tags" options="{'no_create': True}"/>
                        </group>
                        <group>
                            <field name="current_rate"/>
                            <field name="new_rate"/>
                            <field name="update_valuation"/>
                        </group>
                    </group>
                    <footer>
                        <button name="action_update_rate" string="Update Rate" type="object" class="btn-primary"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_update_stock_move_rate" model="ir.actions.act_window">
        <field name="name">Update Stock Move Rate</field>
        <field name="res_model">update.stock.move.rate</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="binding_model_id" ref="model_mrp_production"/>
        <field name="binding_view_types">form</field>
    </record>
</odoo>
