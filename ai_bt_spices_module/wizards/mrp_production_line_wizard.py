# from odoo import models, fields, api

# class MrpProductionLineWizard(models.TransientModel):
#     _name = 'mrp.production.line.wizard'
#     _description = 'Wizard to add multiple Production Lines'

#     production_lines = fields.One2many('mrp.production.line', 'wizard_id', string='Production Lines')

#     @api.model_create_multi
#     def create_lines(self):
#         for line in self.production_lines:
#             line.create({'name': line.name, 'other_field': line.other_field})  # Adjust fields as necessary

#     @api.model
#     def default_get(self, fields):
#         res = super(MrpProductionLineWizard, self).default_get(fields)
#         # Initialize any default values if necessary
#         return res
