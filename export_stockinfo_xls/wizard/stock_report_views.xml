<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record model='ir.ui.view' id='stock_xls_report_view_form'>
        <!--Stock Report form view-->
        <field name="name">stock.xls.report.view.form</field>
        <field name="model">stock.xls.report</field>
        <field name="arch" type="xml">
            <form string="Wizard">
                <group string="Warehouse">
                    <field name="warehouse_ids" widget="many2many_tags"/>
                </group>
                <notebook>
                    <page string="Category">
                        <field name="category_ids">
                            <list>
                                <field name="display_name"/>
                            </list>
                        </field>
                    </page>
                </notebook>
                <footer>
                    <button name="export_xls" type="object" default_focus="1"
                            string="Export Product with Stock Info"
                            class="oe_highlight"
                            context="{'xls_export':1}" icon="fa-download"/>
                    <button string="Cancel" class="oe_link" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>
    <record model='ir.actions.act_window' id='stock_xls_report_action'>
        <!--Stock Report form view action-->
        <field name="name">Export product stock in Excel</field>
        <field name="res_model">stock.xls.report</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="stock_xls_report_view_form"/>
        <field name="target">new</field>
    </record>
    <!--menu action for Current stock in Excel-->
    <menuitem name="Current Stock in Excel"
              parent="stock.menu_warehouse_report"
              id="export_excel"
              action="stock_xls_report_action"
              sequence="3"/>
</odoo>
