<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--Record action for new model -->
    <record id="turnover_graph_analysis_action" model="ir.actions.act_window">
        <field name="name">Turnover Report</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">turnover.graph.analysis</field>
        <field name="view_mode">graph,list</field>
        <field name="target">new</field>
    </record>
    <!--List view of the model -->
    <record id="turnover_graph_analysis_view_list" model="ir.ui.view">
        <field name="name">turnover.graph.analysis.view.list</field>
        <field name="model">turnover.graph.analysis</field>
        <field name="arch" type="xml">
            <list>
                <field name="company_id"/>
                <field name="warehouse_id"/>
                <field name="product_id"/>
                <field name="category_id"/>
                <field name="opening_stock"/>
                <field name="closing_stock"/>
                <field name="average_stock"/>
                <field name="sale_count"/>
                <field name="purchase_count"/>
                <field name="turnover_ratio"/>
            </list>
        </field>
    </record>
    <!--Shows the graph view of the model -->
    <record id="turnover_graph_analysis_view_graph" model="ir.ui.view">
        <field name="name">turnover.graph.analysis.view.graph</field>
        <field name="model">turnover.graph.analysis</field>
        <field name="arch" type="xml">
            <graph string="Turnover Report" type="bar">
                <field name="product_id"/>
                <field name="turnover_ratio" type="measure"/>
            </graph>
        </field>
    </record>
</odoo>
