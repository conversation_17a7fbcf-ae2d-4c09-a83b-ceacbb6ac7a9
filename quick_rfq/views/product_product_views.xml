<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <!-- Server action for rfq creation -->
    <record id="action_product_rfq" model="ir.actions.server">
        <field name="name">Create RFQ</field>
        <field name="groups_id" eval="[(4, ref('purchase.group_purchase_user'))]"/>
        <field name="model_id" ref="product.model_product_product"/>
        <field name="binding_model_id" ref="product.model_product_product"/>
        <field name="binding_view_types">list</field>
        <field name="state">code</field>
        <field name="code">
                if records:
                    action = records.action_create_rfq()
        </field>
    </record>
</odoo>
