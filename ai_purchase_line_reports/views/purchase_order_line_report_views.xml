<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- ========================================
         ENHANCED PURCHASE ORDER LINE VIEWS FOR REPORTING
         ======================================== -->

    <!-- Enhanced Search View with Advanced Filters and Group By -->
    <record id="view_purchase_order_line_search_enhanced" model="ir.ui.view">
        <field name="name">purchase.order.line.search.enhanced</field>
        <field name="model">purchase.order.line</field>
        <field name="arch" type="xml">
            <search string="Purchase Order Line Analysis">
                <!-- Search Fields -->
                <field name="order_id" string="Purchase Order"/>
                <field name="partner_id" string="Vendor"/>
                <field name="product_id" string="Product"/>
                <field name="name" string="Description"/>

                <!-- Quick Filters -->
                <separator/>
                <filter string="To Receive" name="to_receive" domain="[('qty_received', '&lt;', 'product_qty')]"/>
                <filter string="Fully Received" name="received" domain="[('qty_received', '>=', 'product_qty')]"/>
                <separator/>


                <!-- Group By Options -->
                <group expand="0" string="Group By">
                    <filter string="Vendor" name="group_partner" context="{'group_by': 'partner_id'}"/>
                    <filter string="Purchase Order" name="group_order" context="{'group_by': 'order_id'}"/>
                    <filter string="Product" name="group_product" context="{'group_by': 'product_id'}"/>
                    <separator/>
                    <filter string="Scheduled Date" name="group_date_planned" context="{'group_by': 'date_planned:month'}"/>
                    <separator/>
                    <filter string="Company" name="group_company" context="{'group_by': 'company_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Enhanced List View with All Custom Fields -->
    <record id="view_purchase_order_line_list_enhanced" model="ir.ui.view">
        <field name="name">purchase.order.line.list.enhanced</field>
        <field name="model">purchase.order.line</field>
        <field name="arch" type="xml">
            <list string="Purchase Order Line Analysis" create="false" edit="false" delete="false"
                  default_order="date_order desc, order_id desc, sequence"

                  decoration-danger="qty_received &lt; product_qty">

                <!-- Core Fields -->
                <field name="order_id" optional="show" widget="many2one_clickable"/>
                <field name="partner_id" optional="show"/>
                <field name="date_order" optional="show"/>


                <!-- Product Information -->
                <field name="product_id" optional="show"/>
                <field name="name" optional="hide"/>
                <field name="product_uom" optional="hide"/>

                <!-- Quantities & Pricing -->
                <field name="product_qty" optional="show" sum="Total Quantity"/>
                <field name="qty_received" optional="show" sum="Total Received"/>
                <field name="qty_invoiced" optional="hide" sum="Total Invoiced"/>
                <field name="price_unit" optional="show"/>
                <field name="price_subtotal" optional="show" sum="Total Subtotal"/>
                <field name="price_total" optional="hide" sum="Total Amount"/>

                <!-- Status Fields -->
                <field name="qty_to_invoice" optional="hide" sum="Total To Invoice"/>

                <!-- Payment Fields (Optional - only if AI_BT_SPICES_MODULE installed) -->
                <field name="x_product_rate" optional="hide" sum="Total Product Rate"/>
                <field name="x_cash_payment" optional="hide" sum="Total Cash Payment"/>
                <field name="x_bank_payment" optional="hide" sum="Total Bank Payment"/>

                <!-- Dates -->
                <field name="date_planned" optional="hide"/>

                <!-- Additional Info -->
                <field name="sequence" optional="hide"/>
                <field name="company_id" optional="hide" groups="base.group_multi_company"/>
                <field name="currency_id" optional="hide"/>

                <!-- Hidden fields for decorations -->
                <field name="date_planned" invisible="1"/>
            </list>
        </field>
    </record>

    <!-- Graph View for Visual Analytics -->
    <record id="view_purchase_order_line_graph_enhanced" model="ir.ui.view">
        <field name="name">purchase.order.line.graph.enhanced</field>
        <field name="model">purchase.order.line</field>
        <field name="arch" type="xml">
            <graph string="Purchase Order Line Analysis" type="bar" stacked="True">
                <field name="partner_id" type="row"/>
                <field name="price_subtotal" type="measure"/>
                <field name="product_qty" type="measure"/>
                <field name="qty_received" type="measure"/>
                <field name="qty_invoiced" type="measure"/>

            </graph>
        </field>
    </record>

    <!-- Pivot View for Cross-tabulation Analysis -->
    <record id="view_purchase_order_line_pivot_enhanced" model="ir.ui.view">
        <field name="name">purchase.order.line.pivot.enhanced</field>
        <field name="model">purchase.order.line</field>
        <field name="arch" type="xml">
            <pivot string="Purchase Order Line Analysis">
                <field name="partner_id" type="row"/>
                <field name="product_id" type="row"/>
                <field name="price_subtotal" type="measure"/>
                <field name="product_qty" type="measure"/>
                <field name="qty_received" type="measure"/>
                <field name="qty_invoiced" type="measure"/>
            </pivot>
        </field>
    </record>

    <!-- Action for Purchase Order Line Reports -->
    <record id="action_purchase_order_line_report_enhanced" model="ir.actions.act_window">
        <field name="name">Purchase Order Line Analysis</field>
        <field name="res_model">purchase.order.line</field>
        <field name="view_mode">list,graph,pivot</field>
        <field name="view_ids" eval="[(5, 0, 0),
                                      (0, 0, {'view_mode': 'list', 'view_id': ref('view_purchase_order_line_list_enhanced')}),
                                      (0, 0, {'view_mode': 'graph', 'view_id': ref('view_purchase_order_line_graph_enhanced')}),
                                      (0, 0, {'view_mode': 'pivot', 'view_id': ref('view_purchase_order_line_pivot_enhanced')})]"/>
        <field name="search_view_id" ref="view_purchase_order_line_search_enhanced"/>
        <field name="context">{
            'search_default_group_partner': 1
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No purchase order lines found!
            </p>
            <p>
                This enhanced view provides comprehensive analysis of purchase order lines with support for all custom fields.
                Use the filters and group by options to analyze your purchase data effectively.
            </p>
            <p>
                <strong>Features:</strong>
            </p>
            <ul>
                <li>Smart field visibility controls (show/hide columns)</li>
                <li>Advanced filtering and grouping options</li>
                <li>Support for custom fields from all modules</li>
                <li>Visual analytics with graphs and pivot tables</li>
                <li>Real-time data from purchase order lines</li>
            </ul>
        </field>
    </record>

    <!-- Menu Item under Purchase -->
    <menuitem id="menu_purchase_order_line_analysis"
              name="Purchase Line Analysis"
              parent="purchase.menu_purchase_root"
              action="action_purchase_order_line_report_enhanced"
              sequence="100"
              groups="purchase.group_purchase_user"/>

</odoo>