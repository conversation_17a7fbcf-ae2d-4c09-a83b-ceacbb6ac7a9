<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- Record Rules -->
        <record id="sales_agent_commission_admin_rule" model="ir.rule">
            <field name="name">Sales Agent Commission: Admin Full Access</field>
            <field name="model_id" ref="model_sales_agent_commission"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('base.group_system'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <record id="sales_agent_commission_manager_rule" model="ir.rule">
            <field name="name">Sales Agent Commission: Sales Manager Full Access</field>
            <field name="model_id" ref="model_sales_agent_commission"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('sales_team.group_sale_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <record id="sales_agent_commission_user_rule" model="ir.rule">
            <field name="name">Sales Agent Commission: User Own Orders</field>
            <field name="model_id" ref="model_sales_agent_commission"/>
            <field name="domain_force">[('sale_order_id.user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('sales_team.group_sale_salesman'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
    </data>
</odoo>
