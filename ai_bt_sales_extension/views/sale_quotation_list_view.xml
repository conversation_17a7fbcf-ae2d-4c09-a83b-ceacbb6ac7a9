<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Inherit Sale Quotation List View -->
    <record id="view_quotation_list_inherit" model="ir.ui.view">
        <field name="name">sale.quotation.list.inherit</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_quotation_tree"/>
        <field name="arch" type="xml">
            <!-- In the core view, date_order is replaced with create_date.
                 We want to show both, with create_date as optional="hide" -->
            <xpath expr="//field[@name='create_date']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>

            <!-- Add date_order back with optional="show" -->
            <xpath expr="//field[@name='create_date']" position="after">
                <field name="date_order" optional="show"/>
            </xpath>
        </field>
    </record>
</odoo>
