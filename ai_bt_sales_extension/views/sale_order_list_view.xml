<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Inherit Sale Order List View -->
    <record id="view_sale_order_list_inherit" model="ir.ui.view">
        <field name="name">sale.order.list.inherit</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.sale_order_tree"/>
        <field name="arch" type="xml">
            <!-- Add a dummy field modification to ensure the view is not empty -->
            <field name="commitment_date" position="attributes">
                <attribute name="optional">hide</attribute>
            </field>

            <!-- Note: date_order already exists with optional="show" in the core view, no need to add it -->
            <!-- Note: create_date field is only in the quotation view, not in the sales order view.
                 See sale_quotation_list_view.xml for create_date modifications. -->
            <!-- Note: The product and lot field width adjustments are for the form view's order lines,
                 not for the list view. These will be applied when viewing a specific order. -->
        </field>
    </record>

    <!-- This comment is for documentation purposes only -->
    <!-- The date_order field is already editable in Quotation state in the core view.
         It has readonly="state in ['cancel', 'sale']" which means it's editable in 'draft' and 'sent' states.
         No need to create a separate view for it. -->
</odoo>
