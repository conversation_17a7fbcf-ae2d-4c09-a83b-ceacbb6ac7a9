<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Inherit Sale Order Form -->
    <record id="view_order_form_inherit_sales_agent" model="ir.ui.view">
        <field name="name">sale.order.form.inherit.sales.agent</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='payment_term_id']" position="after">
                <field name="x_commission_fees" readonly="state not in ['draft']"/>
                <field name="x_commission_fees_values" 
                       invisible="not x_commission_fees" 
                       readonly="state not in ['draft']" 
                       string="Commission Percentage"/>
                <field name="x_sales_agent_id" 
                       invisible="not x_commission_fees"
                       readonly="state not in ['draft']"/>
            </xpath>
        </field>
    </record>

    <!-- Inherit Partner Form -->
    <record id="view_partner_form_inherit_sales_agent" model="ir.ui.view">
        <field name="name">res.partner.form.inherit.sales.agent</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='category_id']" position="after">
                <field name="x_is_sales_agent"/>
            </xpath>
        </field>
    </record>
</odoo>
