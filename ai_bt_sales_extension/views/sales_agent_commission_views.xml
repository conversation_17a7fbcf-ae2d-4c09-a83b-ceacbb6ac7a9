<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- list View for Sales Agent Commission -->
    <record id="view_sales_agent_commission_list" model="ir.ui.view">
        <field name="name">sales.agent.commission.list</field>
        <field name="model">sales.agent.commission</field>
        <field name="arch" type="xml">
            <list string="Sales Agent Commission">
                <field name="sale_order_id"/>
                <field name="sales_agent_id"/>
                <field name="sale_date"/>
                <field name="delivery_date"/>
                <field name="commission_amount" sum="Total Commission" widget="monetary"/>
                <field name="amount_paid" sum="Total Paid" widget="monetary"/>
                <field name="due_amount" sum="Total Due" widget="monetary"/>                
            </list>
        </field>
    </record>

    <!-- Form View for Sales Agent Commission -->
    <record id="view_sales_agent_commission_form" model="ir.ui.view">
        <field name="name">sales.agent.commission.form</field>
        <field name="model">sales.agent.commission</field>
        <field name="arch" type="xml">
            <form string="Sales Agent Commission">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="sale_order_id" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="sales_agent_id"/>
                            <field name="sale_date"/>
                            <field name="delivery_date"/>
                        </group>
                        <group>
                            <field name="commission_amount" widget="monetary"/>
                            <field name="amount_paid" widget="monetary"/>
                            <field name="due_amount" widget="monetary"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Search View for Sales Agent Commission -->
    <record id="view_sales_agent_commission_search" model="ir.ui.view">
        <field name="name">sales.agent.commission.search</field>
        <field name="model">sales.agent.commission</field>
        <field name="arch" type="xml">
            <search string="Sales Agent Commission">
                <field name="sale_order_id"/>
                <field name="sales_agent_id"/>
                <field name="sale_date"/>
                <field name="delivery_date"/>
                <separator/>
                <filter string="Unpaid Commission" name="unpaid" domain="[('due_amount', '>', 0)]"/>
                <filter string="Paid Commission" name="paid" domain="[('due_amount', '=', 0)]"/>
                <group expand="0" string="Group By">
                    <filter string="Sales Agent" name="group_by_agent" context="{'group_by': 'sales_agent_id'}"/>
                    <filter string="Sale Order" name="group_by_order" context="{'group_by': 'sale_order_id'}"/>
                    <filter string="Sale Date" name="group_by_date" context="{'group_by': 'sale_date:month'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action for Sales Agent Commission -->
    <record id="action_sales_agent_commission" model="ir.actions.act_window">
        <field name="name">Sales Agent Commission</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">sales.agent.commission</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_sales_agent_commission_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first sales agent commission!
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_sales_agent_commission"
              name="Sales Agent Commission"
              action="action_sales_agent_commission"
              parent="sale.sale_menu_root"
              sequence="20"/>
</odoo>