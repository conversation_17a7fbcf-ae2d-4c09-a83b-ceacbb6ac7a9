<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>
        <record id="sale_order_form" model="ir.ui.view">
            <field name="name">sale.order.form</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form"/>
            <field name="arch" type="xml">
                <!-- <xpath expr="//div[@name='button_box']" position="inside">
                    <button name="action_register_payment" 
                            type="object" 
                            string="Register Payment" 
                            class="oe_highlight oe_custom_payment_btn" 
                            invisible="state not in ['sale', 'done']"
                            groups="account.group_account_invoice"
                            help="Register payment using custom method"/>
                </xpath> -->
               
               
                <xpath expr="//field[@name='order_line']/list//field[@name='price_unit']" position="after">
                    <field name="x_product_rate"/>
                    <field name="x_bank_payment"/>
                    <field name="x_cash_payment"/>
                    <field name="x_bag"/>
                    <field name="x_bag_quantity"/>
                    <field name="x_product_loose_weight" readonly="state not in ['draft']"/>
                    <field name="x_product_total_weight" readonly="1"/>

                </xpath>
                <xpath expr="//field[@name='order_line']/list//field[@name='tax_id']" position="attributes">
                    <attribute name="optional">hide</attribute>
                </xpath>
                <xpath expr="//field[@name='tax_totals']" position="before">
                    <field name="total_bank_payment"/>
                    <field name="total_cash_payment"/>
                </xpath>

                <xpath expr="//field[@name='order_line']/list//field[@name='price_unit']" position="attributes">    
                    <attribute name="optional">hide</attribute>
                </xpath>

            </field>
        </record>

        <record id="sale_order_line_form_view" model="ir.ui.view">
            <field name="name">sale.order.line.form.readonly</field>
            <field name="model">sale.order.line</field>
            <field name="inherit_id" ref="sale.sale_order_line_view_form_readonly"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='product_uom_qty']" position="after">
                    <field name="x_bag"/>
                    <field name="x_bag_quantity"/>

                </xpath>
            </field>
        </record>
        <!-- <record id="view_product_template_form_inherit_negative_inventory" model="ir.ui.view">
            <field name="name">product.template.form.inherit.negative.inventory</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='type']" position="after">
                    <field name="allow_negative_inventory"/>
                </xpath>
            </field>
        </record> -->
    </data>
</odoo>
