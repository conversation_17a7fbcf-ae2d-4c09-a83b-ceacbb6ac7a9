from odoo import models, fields, api
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)

class SaleOrder(models.Model):
    _inherit = 'sale.order'


    @api.depends('x_sales_agent_id')
    def _compute_sales_agent_fees(self):
        for order in self:
            if not order.x_sales_agent_id:
                order.x_commission_fees = False
                sale_order_lines = self.env['sale.order.line'].search([('order_id', '=', order.id)])
                for line in sale_order_lines:
                    if line.product_id.name == 'Sales Agent Commission':
                        _logger.info('Sales Agent Commission Line ID : ' + str(line.id))
                        line.unlink()

    @api.onchange('x_sales_agent_id')
    def _onchange_sales_agent_fees(self):
        for order in self:
            if not order.x_sales_agent_id:
                order.x_commission_fees = False

    def action_confirm(self):
        res = super(SaleOrder, self).action_confirm()
        return res
