from odoo import models, fields, api
from odoo.exceptions import UserError
from datetime import datetime
import logging

_logger = logging.getLogger(__name__)

class SalesAgentCommission(models.Model):
    _name = 'sales.agent.commission'
    _description = 'Sales Agent Commission Tracking'
    _sql_constraints = [
        ('unique_sale_order', 'UNIQUE(sale_order_id)',
         'Only one commission record can exist per sale order!')
    ]
    
    # Linking with Sale Order
    sale_order_id = fields.Many2one('sale.order', string="Sale Order", required=True)
    
    # Linking Sales Agent (filtered based on res.partner with x_is_sales_agent)
    sales_agent_id = fields.Many2one('res.partner', string="Sales Agent", 
                                    domain=[('x_is_sales_agent', '=', True)], 
                                    required=True)
    
    # Sale Date (Auto-filled from Sale Order)
    sale_date = fields.Datetime(related='sale_order_id.date_order', string="Sale Date", readonly=True)
    
    # Delivery Date (Auto-filled from stock.picking)
    delivery_date = fields.Date(string="Delivery Date", readonly=True)
    
    # Commission Amount (Computed based on sale order amount and commission percentage)
    commission_amount = fields.Float(string="Commission Amount", compute='_compute_commission_amount', store=True)
    
    # Amount Paid (Manually entered by the user)
    amount_paid = fields.Float(string="Amount Already Paid", default=0.0)
    
    # Due Amount
    due_amount = fields.Float(string="Due Amount", compute='_compute_due_amount', store=True)

    @api.depends('sale_order_id', 'sale_order_id.amount_total', 'sale_order_id.x_commission_fees_values', 'sale_order_id.order_line.x_bag')
    def _compute_commission_amount(self):
        for record in self:
            if record.sale_order_id and record.sale_order_id.x_commission_fees:
                # Calculate total amount excluding bag products
                total_without_bags = sum(
                    line.price_subtotal 
                    for line in record.sale_order_id.order_line 
                    if not line.product_id.x_is_bag
                )
                record.commission_amount = (total_without_bags * 
                                         record.sale_order_id.x_commission_fees_values)
            else:
                record.commission_amount = 0.0

    @api.depends('commission_amount', 'amount_paid')
    def _compute_due_amount(self):
        for record in self:
            record.due_amount = record.commission_amount - record.amount_paid
