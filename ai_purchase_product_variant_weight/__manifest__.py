{
    'name': 'Purchase Order Product Variant Weight',
    'version': '********.0',
    'category': 'Purchase',
    'summary': 'Extends Purchase Order with Reference Product Variant and <PERSON><PERSON><PERSON> (Weight) fields',
    'description': """
        This module extends Purchase Order with:
        - Reference Product Variant field: Select an existing product as a reference
        - <PERSON><PERSON><PERSON> (Weight) field: Specify the weight for the new product variant
        - <PERSON><PERSON> to create new products with updated weight and add them to the order
        - Automatically updates product name by replacing only the weight part while maintaining the naming convention
        - Example: From 'AJMO DIAMOND 500G [30KG]' to 'AJMO DIAMOND 50G [30KG]' when changing the weight
        - Adds a new group in the Purchase Order form after the Broker field from ai_bt_spices_module

        Features:
        - Intelligent weight pattern detection in product names
        - Proper handling of package weights in square brackets
        - Automatic conversion between grams and kilograms based on weight value
        - Reuse of existing products with the same name to avoid duplicates
        - Only creates a new order line if the product doesn't already exist in the order
        - Comprehensive logging for easy debugging and monitoring
    """,
    'author': 'Arihant Ai',
    'website': 'https://www.arihantai.com',
    'depends': ['purchase', 'product', 'ai_bt_spices_module'],
    'data': [
        'views/purchase_views.xml',
    ],
    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
}
